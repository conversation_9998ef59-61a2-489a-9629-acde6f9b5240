('C:\\Users\\<USER>\\Desktop\\授权最新文件\\新建文件夹\\新建文件夹 (完整版) - '
 '副本\\build\\amazon_blueprint\\PYZ-00.pyz',
 [('PIL',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.AvifImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\AvifImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._typing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._util',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._version',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('__future__',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\__future__.py',
   'PYMODULE'),
  ('_aix_support',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('_compat_pickle',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_compression.py',
   'PYMODULE'),
  ('_markupbase',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_markupbase.py',
   'PYMODULE'),
  ('_py_abc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_py_abc.py',
   'PYMODULE'),
  ('_pydecimal',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_pydecimal.py',
   'PYMODULE'),
  ('_strptime',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\argparse.py',
   'PYMODULE'),
  ('ast',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ast.py',
   'PYMODULE'),
  ('asyncio',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.transports',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('auto_updater',
   'C:\\Users\\<USER>\\Desktop\\授权最新文件\\新建文件夹\\新建文件夹 (完整版) - 副本\\auto_updater.py',
   'PYMODULE'),
  ('base64',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\base64.py',
   'PYMODULE'),
  ('bisect',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\bisect.py',
   'PYMODULE'),
  ('brotli',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\brotli.py',
   'PYMODULE'),
  ('bs4',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\bs4\\__init__.py',
   'PYMODULE'),
  ('bs4._deprecation',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\bs4\\_deprecation.py',
   'PYMODULE'),
  ('bs4._typing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\bs4\\_typing.py',
   'PYMODULE'),
  ('bs4._warnings',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\bs4\\_warnings.py',
   'PYMODULE'),
  ('bs4.builder',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\bs4\\builder\\__init__.py',
   'PYMODULE'),
  ('bs4.builder._html5lib',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\bs4\\builder\\_html5lib.py',
   'PYMODULE'),
  ('bs4.builder._htmlparser',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\bs4\\builder\\_htmlparser.py',
   'PYMODULE'),
  ('bs4.builder._lxml',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\bs4\\builder\\_lxml.py',
   'PYMODULE'),
  ('bs4.css',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\bs4\\css.py',
   'PYMODULE'),
  ('bs4.dammit',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\bs4\\dammit.py',
   'PYMODULE'),
  ('bs4.element',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\bs4\\element.py',
   'PYMODULE'),
  ('bs4.exceptions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\bs4\\exceptions.py',
   'PYMODULE'),
  ('bs4.filter',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\bs4\\filter.py',
   'PYMODULE'),
  ('bs4.formatter',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\bs4\\formatter.py',
   'PYMODULE'),
  ('bz2',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\bz2.py',
   'PYMODULE'),
  ('calendar',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\calendar.py',
   'PYMODULE'),
  ('certifi',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('cgi',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\cgi.py',
   'PYMODULE'),
  ('charset_normalizer',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('colorama',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorsys',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\colorsys.py',
   'PYMODULE'),
  ('commctrl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32\\lib\\commctrl.py',
   'PYMODULE'),
  ('concurrent',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\contextvars.py',
   'PYMODULE'),
  ('copy',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\copy.py',
   'PYMODULE'),
  ('cryptography',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.fernet',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\fernet.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hmac',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\hmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.kdf',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\kdf\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.kdf.pbkdf2',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\kdf\\pbkdf2.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.padding',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.x509',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE'),
  ('csv',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\csv.py',
   'PYMODULE'),
  ('ctypes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('dataclasses',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\datetime.py',
   'PYMODULE'),
  ('dateutil',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil._version',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('decimal',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\decimal.py',
   'PYMODULE'),
  ('difflib',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\difflib.py',
   'PYMODULE'),
  ('dis',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\dis.py',
   'PYMODULE'),
  ('dotenv',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\dotenv\\__init__.py',
   'PYMODULE'),
  ('dotenv.ipython',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\dotenv\\ipython.py',
   'PYMODULE'),
  ('dotenv.main',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\dotenv\\main.py',
   'PYMODULE'),
  ('dotenv.parser',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\dotenv\\parser.py',
   'PYMODULE'),
  ('dotenv.variables',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\dotenv\\variables.py',
   'PYMODULE'),
  ('email',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\utils.py',
   'PYMODULE'),
  ('et_xmlfile',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('et_xmlfile.incremental_tree',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('fake_useragent',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fake_useragent\\__init__.py',
   'PYMODULE'),
  ('fake_useragent.errors',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fake_useragent\\errors.py',
   'PYMODULE'),
  ('fake_useragent.fake',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fake_useragent\\fake.py',
   'PYMODULE'),
  ('fake_useragent.get_version',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fake_useragent\\get_version.py',
   'PYMODULE'),
  ('fake_useragent.log',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fake_useragent\\log.py',
   'PYMODULE'),
  ('fake_useragent.utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fake_useragent\\utils.py',
   'PYMODULE'),
  ('fileinput',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\fileinput.py',
   'PYMODULE'),
  ('fnmatch',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\fnmatch.py',
   'PYMODULE'),
  ('fractions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\fractions.py',
   'PYMODULE'),
  ('fsspec',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\__init__.py',
   'PYMODULE'),
  ('fsspec._version',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\_version.py',
   'PYMODULE'),
  ('fsspec.archive',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\archive.py',
   'PYMODULE'),
  ('fsspec.asyn',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\asyn.py',
   'PYMODULE'),
  ('fsspec.caching',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\caching.py',
   'PYMODULE'),
  ('fsspec.callbacks',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\callbacks.py',
   'PYMODULE'),
  ('fsspec.compression',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\compression.py',
   'PYMODULE'),
  ('fsspec.config',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\config.py',
   'PYMODULE'),
  ('fsspec.conftest',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\conftest.py',
   'PYMODULE'),
  ('fsspec.core',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\core.py',
   'PYMODULE'),
  ('fsspec.dircache',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\dircache.py',
   'PYMODULE'),
  ('fsspec.exceptions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\exceptions.py',
   'PYMODULE'),
  ('fsspec.fuse',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\fuse.py',
   'PYMODULE'),
  ('fsspec.generic',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\generic.py',
   'PYMODULE'),
  ('fsspec.gui',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\gui.py',
   'PYMODULE'),
  ('fsspec.implementations',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\__init__.py',
   'PYMODULE'),
  ('fsspec.implementations.arrow',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\arrow.py',
   'PYMODULE'),
  ('fsspec.implementations.asyn_wrapper',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\asyn_wrapper.py',
   'PYMODULE'),
  ('fsspec.implementations.cache_mapper',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\cache_mapper.py',
   'PYMODULE'),
  ('fsspec.implementations.cache_metadata',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\cache_metadata.py',
   'PYMODULE'),
  ('fsspec.implementations.cached',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\cached.py',
   'PYMODULE'),
  ('fsspec.implementations.dask',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\dask.py',
   'PYMODULE'),
  ('fsspec.implementations.data',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\data.py',
   'PYMODULE'),
  ('fsspec.implementations.dbfs',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\dbfs.py',
   'PYMODULE'),
  ('fsspec.implementations.dirfs',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\dirfs.py',
   'PYMODULE'),
  ('fsspec.implementations.ftp',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\ftp.py',
   'PYMODULE'),
  ('fsspec.implementations.gist',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\gist.py',
   'PYMODULE'),
  ('fsspec.implementations.git',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\git.py',
   'PYMODULE'),
  ('fsspec.implementations.github',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\github.py',
   'PYMODULE'),
  ('fsspec.implementations.http',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\http.py',
   'PYMODULE'),
  ('fsspec.implementations.http_sync',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\http_sync.py',
   'PYMODULE'),
  ('fsspec.implementations.jupyter',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\jupyter.py',
   'PYMODULE'),
  ('fsspec.implementations.libarchive',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\libarchive.py',
   'PYMODULE'),
  ('fsspec.implementations.local',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\local.py',
   'PYMODULE'),
  ('fsspec.implementations.memory',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\memory.py',
   'PYMODULE'),
  ('fsspec.implementations.reference',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\reference.py',
   'PYMODULE'),
  ('fsspec.implementations.sftp',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\sftp.py',
   'PYMODULE'),
  ('fsspec.implementations.smb',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\smb.py',
   'PYMODULE'),
  ('fsspec.implementations.tar',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\tar.py',
   'PYMODULE'),
  ('fsspec.implementations.webhdfs',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\webhdfs.py',
   'PYMODULE'),
  ('fsspec.implementations.zip',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\implementations\\zip.py',
   'PYMODULE'),
  ('fsspec.json',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\json.py',
   'PYMODULE'),
  ('fsspec.mapping',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\mapping.py',
   'PYMODULE'),
  ('fsspec.parquet',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\parquet.py',
   'PYMODULE'),
  ('fsspec.registry',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\registry.py',
   'PYMODULE'),
  ('fsspec.spec',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\spec.py',
   'PYMODULE'),
  ('fsspec.transaction',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\transaction.py',
   'PYMODULE'),
  ('fsspec.utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fsspec\\utils.py',
   'PYMODULE'),
  ('ftplib',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ftplib.py',
   'PYMODULE'),
  ('getopt',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\getopt.py',
   'PYMODULE'),
  ('getpass',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\getpass.py',
   'PYMODULE'),
  ('gettext',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\gettext.py',
   'PYMODULE'),
  ('glob',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\glob.py',
   'PYMODULE'),
  ('gzip',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\gzip.py',
   'PYMODULE'),
  ('hashlib',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\hashlib.py',
   'PYMODULE'),
  ('hmac',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\hmac.py',
   'PYMODULE'),
  ('html',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\html\\entities.py',
   'PYMODULE'),
  ('html.parser',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\html\\parser.py',
   'PYMODULE'),
  ('http',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.client',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\http\\cookies.py',
   'PYMODULE'),
  ('idna',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._adapters',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\_adapters.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib._common',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\_common.py',
   'PYMODULE'),
  ('importlib.abc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('importlib.util',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\inspect.py',
   'PYMODULE'),
  ('ipaddress',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ipaddress.py',
   'PYMODULE'),
  ('jinja2',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\jinja2\\__init__.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.constants',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.debug',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.environment',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.ext',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\jinja2\\ext.py',
   'PYMODULE'),
  ('jinja2.filters',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.parser',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.tests',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('json',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lxml',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\__init__.py',
   'PYMODULE'),
  ('lxml.ElementInclude',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\ElementInclude.py',
   'PYMODULE'),
  ('lxml.cssselect',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\cssselect.py',
   'PYMODULE'),
  ('lxml.doctestcompare',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\doctestcompare.py',
   'PYMODULE'),
  ('lxml.html',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\html\\__init__.py',
   'PYMODULE'),
  ('lxml.html.ElementSoup',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\html\\ElementSoup.py',
   'PYMODULE'),
  ('lxml.html._diffcommand',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\html\\_diffcommand.py',
   'PYMODULE'),
  ('lxml.html._html5builder',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\html\\_html5builder.py',
   'PYMODULE'),
  ('lxml.html._setmixin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\html\\_setmixin.py',
   'PYMODULE'),
  ('lxml.html.builder',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\html\\builder.py',
   'PYMODULE'),
  ('lxml.html.clean',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\html\\clean.py',
   'PYMODULE'),
  ('lxml.html.defs',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\html\\defs.py',
   'PYMODULE'),
  ('lxml.html.formfill',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\html\\formfill.py',
   'PYMODULE'),
  ('lxml.html.html5parser',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\html\\html5parser.py',
   'PYMODULE'),
  ('lxml.html.soupparser',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\html\\soupparser.py',
   'PYMODULE'),
  ('lxml.html.usedoctest',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\html\\usedoctest.py',
   'PYMODULE'),
  ('lxml.includes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\includes\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.extlibs',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\includes\\extlibs\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libexslt',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\includes\\libexslt\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxml',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\includes\\libxml\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxslt',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\includes\\libxslt\\__init__.py',
   'PYMODULE'),
  ('lxml.isoschematron',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\isoschematron\\__init__.py',
   'PYMODULE'),
  ('lxml.pyclasslookup',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\pyclasslookup.py',
   'PYMODULE'),
  ('lxml.usedoctest',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\usedoctest.py',
   'PYMODULE'),
  ('lzma',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\lzma.py',
   'PYMODULE'),
  ('markupsafe',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('mimetypes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\mimetypes.py',
   'PYMODULE'),
  ('multiprocessing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\netrc.py',
   'PYMODULE'),
  ('nturl2path',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\numbers.py',
   'PYMODULE'),
  ('numpy',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy._core',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.records',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.tests', '-', 'PYMODULE'),
  ('numpy._core.tests._natype',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\tests\\_natype.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.char',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy.core',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.f2py',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.fft',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.linalg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matlib',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.rec',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy.testing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\opcode.py',
   'PYMODULE'),
  ('openpyxl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.abc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\compat\\abc.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('openpyxl.compat.product',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\compat\\product.py',
   'PYMODULE'),
  ('openpyxl.compat.singleton',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\compat\\singleton.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.descriptors.container',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\descriptors\\container.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.descriptors.slots',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\descriptors\\slots.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.packaging.interface',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\packaging\\interface.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.utils.dataframe',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\utils\\dataframe.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.utils.inference',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\utils\\inference.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_watch',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\cell_watch.py',
   'PYMODULE'),
  ('openpyxl.worksheet.controls',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\controls.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet.custom',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet.errors',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\errors.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.worksheet.ole',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\ole.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.picture',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\picture.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.smart_tag',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\smart_tag.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('optparse',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\optparse.py',
   'PYMODULE'),
  ('packaging',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._structures',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging.version',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pandas',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE'),
  ('pandas._config',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.config',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas._config.display',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('pandas._libs',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas._testing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE'),
  ('pandas._testing._io',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE'),
  ('pandas._typing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('pandas._version',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas._version_meson',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_version_meson.py',
   'PYMODULE'),
  ('pandas.api',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.api.types',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.api.typing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\api\\typing\\__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.compat',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('pandas.compat._constants',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.compat.compressors',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE'),
  ('pandas.core',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE'),
  ('pandas.core._numba.extensions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\_numba\\extensions.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.api',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_accumulations',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays._arrow_string_mixins',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\_arrow_string_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays._utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.accessors',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\accessors.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.base',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.core.common',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.flags',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.core.methods',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.core.methods.describe',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE'),
  ('pandas.core.methods.selectn',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE'),
  ('pandas.core.methods.to_dict',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE'),
  ('pandas.core.sample',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE'),
  ('pandas.core.series',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.core.util',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.errors',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas.io',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.io._util',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE'),
  ('pandas.io.api',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.common',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.io.excel._calamine',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\excel\\_calamine.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.io.html',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('pandas.io.json',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.io.xml',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE'),
  ('pandas.plotting',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.testing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas.tseries',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.tseries.holiday',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\tseries\\holiday.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.util',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.util.version',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE'),
  ('pathlib',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\pathlib.py',
   'PYMODULE'),
  ('pickle',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\pickle.py',
   'PYMODULE'),
  ('pickletools',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\pickletools.py',
   'PYMODULE'),
  ('pkgutil',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\pkgutil.py',
   'PYMODULE'),
  ('platform',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\platform.py',
   'PYMODULE'),
  ('pprint',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\pprint.py',
   'PYMODULE'),
  ('psutil',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._common',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('py_compile',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\py_compile.py',
   'PYMODULE'),
  ('pythoncom',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pythoncom.py',
   'PYMODULE'),
  ('pytz',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pytz.lazy',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('pywin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\Pythonwin\\pywin\\__init__.py',
   'PYMODULE'),
  ('pywin.dialogs',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\__init__.py',
   'PYMODULE'),
  ('pywin.dialogs.list',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\list.py',
   'PYMODULE'),
  ('pywin.dialogs.status',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\status.py',
   'PYMODULE'),
  ('pywin.mfc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\__init__.py',
   'PYMODULE'),
  ('pywin.mfc.dialog',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\dialog.py',
   'PYMODULE'),
  ('pywin.mfc.object',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\object.py',
   'PYMODULE'),
  ('pywin.mfc.thread',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\thread.py',
   'PYMODULE'),
  ('pywin.mfc.window',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\window.py',
   'PYMODULE'),
  ('pywin32_system32', '-', 'PYMODULE'),
  ('pywintypes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32\\lib\\pywintypes.py',
   'PYMODULE'),
  ('queue',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\queue.py',
   'PYMODULE'),
  ('quopri',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\quopri.py',
   'PYMODULE'),
  ('random',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\random.py',
   'PYMODULE'),
  ('requests',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('runpy',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\runpy.py',
   'PYMODULE'),
  ('secrets',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\secrets.py',
   'PYMODULE'),
  ('selectors',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\selectors.py',
   'PYMODULE'),
  ('selenium',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\__init__.py',
   'PYMODULE'),
  ('selenium.common',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\common\\__init__.py',
   'PYMODULE'),
  ('selenium.common.exceptions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\common\\exceptions.py',
   'PYMODULE'),
  ('selenium.types',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\types.py',
   'PYMODULE'),
  ('selenium.webdriver',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\chrome\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.options',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\chrome\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.remote_connection',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\chrome\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.service',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\chrome\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.webdriver',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\chrome\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\chromium\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.options',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\chromium\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.remote_connection',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\chromium\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.service',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\chromium\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.webdriver',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\chromium\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.common',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\common\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.action_chains',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\common\\action_chains.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.action_builder',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\action_builder.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.input_device',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\input_device.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.interaction',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\interaction.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.key_actions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\key_actions.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.key_input',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\key_input.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.mouse_button',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\mouse_button.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.pointer_actions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\pointer_actions.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.pointer_input',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\pointer_input.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.wheel_actions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\wheel_actions.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.wheel_input',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\wheel_input.py',
   'PYMODULE'),
  ('selenium.webdriver.common.alert',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\common\\alert.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.browser',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\browser.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.browsing_context',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\browsing_context.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.common',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\common.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.log',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\log.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.network',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\network.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.permissions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\permissions.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.script',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\script.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.session',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\session.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.storage',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\storage.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.webextension',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\webextension.py',
   'PYMODULE'),
  ('selenium.webdriver.common.by',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\common\\by.py',
   'PYMODULE'),
  ('selenium.webdriver.common.desired_capabilities',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\common\\desired_capabilities.py',
   'PYMODULE'),
  ('selenium.webdriver.common.driver_finder',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\common\\driver_finder.py',
   'PYMODULE'),
  ('selenium.webdriver.common.fedcm',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\common\\fedcm\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.fedcm.account',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\common\\fedcm\\account.py',
   'PYMODULE'),
  ('selenium.webdriver.common.fedcm.dialog',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\common\\fedcm\\dialog.py',
   'PYMODULE'),
  ('selenium.webdriver.common.keys',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\common\\keys.py',
   'PYMODULE'),
  ('selenium.webdriver.common.options',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\common\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.common.print_page_options',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\common\\print_page_options.py',
   'PYMODULE'),
  ('selenium.webdriver.common.proxy',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\common\\proxy.py',
   'PYMODULE'),
  ('selenium.webdriver.common.selenium_manager',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\common\\selenium_manager.py',
   'PYMODULE'),
  ('selenium.webdriver.common.service',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\common\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.common.timeouts',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\common\\timeouts.py',
   'PYMODULE'),
  ('selenium.webdriver.common.utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\common\\utils.py',
   'PYMODULE'),
  ('selenium.webdriver.common.virtual_authenticator',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\common\\virtual_authenticator.py',
   'PYMODULE'),
  ('selenium.webdriver.edge',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\edge\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.edge.options',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\edge\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.edge.remote_connection',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\edge\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.edge.service',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\edge\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.edge.webdriver',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\edge\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\firefox\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.firefox_binary',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\firefox\\firefox_binary.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.firefox_profile',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\firefox\\firefox_profile.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.options',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\firefox\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.remote_connection',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\firefox\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.service',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\firefox\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.webdriver',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\firefox\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.ie',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\ie\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.ie.options',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\ie\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.ie.service',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\ie\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.ie.webdriver',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\ie\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.remote',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\remote\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.bidi_connection',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\remote\\bidi_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.client_config',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\remote\\client_config.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.command',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\remote\\command.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.errorhandler',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\remote\\errorhandler.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.fedcm',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\remote\\fedcm.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.file_detector',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\remote\\file_detector.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.locator_converter',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\remote\\locator_converter.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.mobile',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\remote\\mobile.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.remote_connection',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\remote\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.script_key',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\remote\\script_key.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.shadowroot',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\remote\\shadowroot.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.switch_to',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\remote\\switch_to.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\remote\\utils.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.webdriver',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\remote\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.webelement',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\remote\\webelement.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.websocket_connection',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\remote\\websocket_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.safari',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\safari\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.options',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\safari\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.remote_connection',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\safari\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.service',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\safari\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.webdriver',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\safari\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.support',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\support\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.support.relative_locator',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\support\\relative_locator.py',
   'PYMODULE'),
  ('selenium.webdriver.support.wait',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\support\\wait.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\webkitgtk\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk.options',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\webkitgtk\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk.service',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\webkitgtk\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk.webdriver',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\webkitgtk\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\wpewebkit\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit.options',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\wpewebkit\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit.service',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\wpewebkit\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit.webdriver',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\wpewebkit\\webdriver.py',
   'PYMODULE'),
  ('shlex',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\shlex.py',
   'PYMODULE'),
  ('shutil',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\shutil.py',
   'PYMODULE'),
  ('signal',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\signal.py',
   'PYMODULE'),
  ('six',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\six.py',
   'PYMODULE'),
  ('socket',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\socket.py',
   'PYMODULE'),
  ('socks',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\socks.py',
   'PYMODULE'),
  ('soupsieve',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\soupsieve\\__init__.py',
   'PYMODULE'),
  ('soupsieve.__meta__',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\soupsieve\\__meta__.py',
   'PYMODULE'),
  ('soupsieve.css_match',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\soupsieve\\css_match.py',
   'PYMODULE'),
  ('soupsieve.css_parser',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\soupsieve\\css_parser.py',
   'PYMODULE'),
  ('soupsieve.css_types',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\soupsieve\\css_types.py',
   'PYMODULE'),
  ('soupsieve.pretty',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\soupsieve\\pretty.py',
   'PYMODULE'),
  ('soupsieve.util',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\soupsieve\\util.py',
   'PYMODULE'),
  ('sqlite3',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('ssl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ssl.py',
   'PYMODULE'),
  ('statistics',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\statistics.py',
   'PYMODULE'),
  ('string',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\string.py',
   'PYMODULE'),
  ('stringprep',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\sysconfig.py',
   'PYMODULE'),
  ('tarfile',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\tarfile.py',
   'PYMODULE'),
  ('tempfile',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\tempfile.py',
   'PYMODULE'),
  ('textwrap',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\textwrap.py',
   'PYMODULE'),
  ('threading',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\threading.py',
   'PYMODULE'),
  ('tkinter',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.dialog',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\tkinter\\dialog.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.ttk',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\tkinter\\ttk.py',
   'PYMODULE'),
  ('token',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\token.py',
   'PYMODULE'),
  ('tokenize',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\tokenize.py',
   'PYMODULE'),
  ('tqdm',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\tqdm\\__init__.py',
   'PYMODULE'),
  ('tqdm._dist_ver',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\tqdm\\_dist_ver.py',
   'PYMODULE'),
  ('tqdm._monitor',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\tqdm\\_monitor.py',
   'PYMODULE'),
  ('tqdm._tqdm_pandas',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\tqdm\\_tqdm_pandas.py',
   'PYMODULE'),
  ('tqdm.cli',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\tqdm\\cli.py',
   'PYMODULE'),
  ('tqdm.gui',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\tqdm\\gui.py',
   'PYMODULE'),
  ('tqdm.notebook',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\tqdm\\notebook.py',
   'PYMODULE'),
  ('tqdm.std',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\tqdm\\std.py',
   'PYMODULE'),
  ('tqdm.utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\tqdm\\utils.py',
   'PYMODULE'),
  ('tqdm.version',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\tqdm\\version.py',
   'PYMODULE'),
  ('tracemalloc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('typing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\typing.py',
   'PYMODULE'),
  ('typing_extensions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('ui_theme',
   'C:\\Users\\<USER>\\Desktop\\授权最新文件\\新建文件夹\\新建文件夹 (完整版) - 副本\\ui_theme.py',
   'PYMODULE'),
  ('update_config',
   'C:\\Users\\<USER>\\Desktop\\授权最新文件\\新建文件夹\\新建文件夹 (完整版) - 副本\\update_config.py',
   'PYMODULE'),
  ('urllib',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib3',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('uu',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\uu.py',
   'PYMODULE'),
  ('uuid',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\uuid.py',
   'PYMODULE'),
  ('webbrowser',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\webbrowser.py',
   'PYMODULE'),
  ('webdriver_manager',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\webdriver_manager\\__init__.py',
   'PYMODULE'),
  ('webdriver_manager.chrome',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\webdriver_manager\\chrome.py',
   'PYMODULE'),
  ('webdriver_manager.core',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\webdriver_manager\\core\\__init__.py',
   'PYMODULE'),
  ('webdriver_manager.core.archive',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\webdriver_manager\\core\\archive.py',
   'PYMODULE'),
  ('webdriver_manager.core.config',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\webdriver_manager\\core\\config.py',
   'PYMODULE'),
  ('webdriver_manager.core.constants',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\webdriver_manager\\core\\constants.py',
   'PYMODULE'),
  ('webdriver_manager.core.download_manager',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\webdriver_manager\\core\\download_manager.py',
   'PYMODULE'),
  ('webdriver_manager.core.driver',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\webdriver_manager\\core\\driver.py',
   'PYMODULE'),
  ('webdriver_manager.core.driver_cache',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\webdriver_manager\\core\\driver_cache.py',
   'PYMODULE'),
  ('webdriver_manager.core.file_manager',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\webdriver_manager\\core\\file_manager.py',
   'PYMODULE'),
  ('webdriver_manager.core.http',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\webdriver_manager\\core\\http.py',
   'PYMODULE'),
  ('webdriver_manager.core.logger',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\webdriver_manager\\core\\logger.py',
   'PYMODULE'),
  ('webdriver_manager.core.manager',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\webdriver_manager\\core\\manager.py',
   'PYMODULE'),
  ('webdriver_manager.core.os_manager',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\webdriver_manager\\core\\os_manager.py',
   'PYMODULE'),
  ('webdriver_manager.core.utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\webdriver_manager\\core\\utils.py',
   'PYMODULE'),
  ('webdriver_manager.drivers',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\webdriver_manager\\drivers\\__init__.py',
   'PYMODULE'),
  ('webdriver_manager.drivers.chrome',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\webdriver_manager\\drivers\\chrome.py',
   'PYMODULE'),
  ('websocket',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\__init__.py',
   'PYMODULE'),
  ('websocket._abnf',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_abnf.py',
   'PYMODULE'),
  ('websocket._app',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py',
   'PYMODULE'),
  ('websocket._cookiejar',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_cookiejar.py',
   'PYMODULE'),
  ('websocket._core',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_core.py',
   'PYMODULE'),
  ('websocket._exceptions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_exceptions.py',
   'PYMODULE'),
  ('websocket._handshake',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_handshake.py',
   'PYMODULE'),
  ('websocket._http',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_http.py',
   'PYMODULE'),
  ('websocket._logging',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_logging.py',
   'PYMODULE'),
  ('websocket._socket',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_socket.py',
   'PYMODULE'),
  ('websocket._ssl_compat',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_ssl_compat.py',
   'PYMODULE'),
  ('websocket._url',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_url.py',
   'PYMODULE'),
  ('websocket._utils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_utils.py',
   'PYMODULE'),
  ('win32com',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32com\\__init__.py',
   'PYMODULE'),
  ('win32com.client',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32com\\client\\__init__.py',
   'PYMODULE'),
  ('win32com.client.CLSIDToClass',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32com\\client\\CLSIDToClass.py',
   'PYMODULE'),
  ('win32com.client.build',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32com\\client\\build.py',
   'PYMODULE'),
  ('win32com.client.dynamic',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32com\\client\\dynamic.py',
   'PYMODULE'),
  ('win32com.client.gencache',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32com\\client\\gencache.py',
   'PYMODULE'),
  ('win32com.client.genpy',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32com\\client\\genpy.py',
   'PYMODULE'),
  ('win32com.client.makepy',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32com\\client\\makepy.py',
   'PYMODULE'),
  ('win32com.client.selecttlb',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32com\\client\\selecttlb.py',
   'PYMODULE'),
  ('win32com.client.util',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32com\\client\\util.py',
   'PYMODULE'),
  ('win32com.server',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32com\\server\\__init__.py',
   'PYMODULE'),
  ('win32com.server.dispatcher',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32com\\server\\dispatcher.py',
   'PYMODULE'),
  ('win32com.server.exception',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32com\\server\\exception.py',
   'PYMODULE'),
  ('win32com.server.policy',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32com\\server\\policy.py',
   'PYMODULE'),
  ('win32com.server.util',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32com\\server\\util.py',
   'PYMODULE'),
  ('win32com.universal',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32com\\universal.py',
   'PYMODULE'),
  ('win32com.util',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32com\\util.py',
   'PYMODULE'),
  ('win32con',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('win32traceutil',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32\\lib\\win32traceutil.py',
   'PYMODULE'),
  ('winerror',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('wmi',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\wmi.py',
   'PYMODULE'),
  ('xlsxwriter',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\__init__.py',
   'PYMODULE'),
  ('xlsxwriter.app',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\app.py',
   'PYMODULE'),
  ('xlsxwriter.chart',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\chart.py',
   'PYMODULE'),
  ('xlsxwriter.chart_area',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\chart_area.py',
   'PYMODULE'),
  ('xlsxwriter.chart_bar',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\chart_bar.py',
   'PYMODULE'),
  ('xlsxwriter.chart_column',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\chart_column.py',
   'PYMODULE'),
  ('xlsxwriter.chart_doughnut',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\chart_doughnut.py',
   'PYMODULE'),
  ('xlsxwriter.chart_line',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\chart_line.py',
   'PYMODULE'),
  ('xlsxwriter.chart_pie',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\chart_pie.py',
   'PYMODULE'),
  ('xlsxwriter.chart_radar',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\chart_radar.py',
   'PYMODULE'),
  ('xlsxwriter.chart_scatter',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\chart_scatter.py',
   'PYMODULE'),
  ('xlsxwriter.chart_stock',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\chart_stock.py',
   'PYMODULE'),
  ('xlsxwriter.chartsheet',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\chartsheet.py',
   'PYMODULE'),
  ('xlsxwriter.color',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\color.py',
   'PYMODULE'),
  ('xlsxwriter.comments',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\comments.py',
   'PYMODULE'),
  ('xlsxwriter.contenttypes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\contenttypes.py',
   'PYMODULE'),
  ('xlsxwriter.core',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\core.py',
   'PYMODULE'),
  ('xlsxwriter.custom',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\custom.py',
   'PYMODULE'),
  ('xlsxwriter.drawing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\drawing.py',
   'PYMODULE'),
  ('xlsxwriter.exceptions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\exceptions.py',
   'PYMODULE'),
  ('xlsxwriter.feature_property_bag',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\feature_property_bag.py',
   'PYMODULE'),
  ('xlsxwriter.format',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\format.py',
   'PYMODULE'),
  ('xlsxwriter.image',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\image.py',
   'PYMODULE'),
  ('xlsxwriter.metadata',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\metadata.py',
   'PYMODULE'),
  ('xlsxwriter.packager',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\packager.py',
   'PYMODULE'),
  ('xlsxwriter.relationships',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\relationships.py',
   'PYMODULE'),
  ('xlsxwriter.rich_value',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\rich_value.py',
   'PYMODULE'),
  ('xlsxwriter.rich_value_rel',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\rich_value_rel.py',
   'PYMODULE'),
  ('xlsxwriter.rich_value_structure',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\rich_value_structure.py',
   'PYMODULE'),
  ('xlsxwriter.rich_value_types',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\rich_value_types.py',
   'PYMODULE'),
  ('xlsxwriter.shape',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\shape.py',
   'PYMODULE'),
  ('xlsxwriter.sharedstrings',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\sharedstrings.py',
   'PYMODULE'),
  ('xlsxwriter.styles',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\styles.py',
   'PYMODULE'),
  ('xlsxwriter.table',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\table.py',
   'PYMODULE'),
  ('xlsxwriter.theme',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\theme.py',
   'PYMODULE'),
  ('xlsxwriter.url',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\url.py',
   'PYMODULE'),
  ('xlsxwriter.utility',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\utility.py',
   'PYMODULE'),
  ('xlsxwriter.vml',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\vml.py',
   'PYMODULE'),
  ('xlsxwriter.workbook',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\workbook.py',
   'PYMODULE'),
  ('xlsxwriter.worksheet',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\worksheet.py',
   'PYMODULE'),
  ('xlsxwriter.xmlwriter',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xlsxwriter\\xmlwriter.py',
   'PYMODULE'),
  ('xml',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.dom',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('yaml',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\yaml\\__init__.py',
   'PYMODULE'),
  ('yaml.composer',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\yaml\\composer.py',
   'PYMODULE'),
  ('yaml.constructor',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\yaml\\constructor.py',
   'PYMODULE'),
  ('yaml.cyaml',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\yaml\\cyaml.py',
   'PYMODULE'),
  ('yaml.dumper',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\yaml\\dumper.py',
   'PYMODULE'),
  ('yaml.emitter',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\yaml\\emitter.py',
   'PYMODULE'),
  ('yaml.error',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\yaml\\error.py',
   'PYMODULE'),
  ('yaml.events',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\yaml\\events.py',
   'PYMODULE'),
  ('yaml.loader',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\yaml\\loader.py',
   'PYMODULE'),
  ('yaml.nodes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\yaml\\nodes.py',
   'PYMODULE'),
  ('yaml.parser',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\yaml\\parser.py',
   'PYMODULE'),
  ('yaml.reader',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\yaml\\reader.py',
   'PYMODULE'),
  ('yaml.representer',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\yaml\\representer.py',
   'PYMODULE'),
  ('yaml.resolver',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\yaml\\resolver.py',
   'PYMODULE'),
  ('yaml.scanner',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\yaml\\scanner.py',
   'PYMODULE'),
  ('yaml.serializer',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\yaml\\serializer.py',
   'PYMODULE'),
  ('yaml.tokens',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\yaml\\tokens.py',
   'PYMODULE'),
  ('zipfile',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\zipfile.py',
   'PYMODULE'),
  ('zipimport',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\zipimport.py',
   'PYMODULE')])
