('C:\\Users\\<USER>\\Desktop\\授权最新文件\\新建文件夹\\新建文件夹 (完整版) - '
 '副本\\build\\亚马逊蓝图工具\\亚马逊蓝图工具.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('O', None, 'OPTION'),
  ('O', None, 'OPTION'),
  ('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'C:\\Users\\<USER>\\Desktop\\授权最新文件\\新建文件夹\\新建文件夹 (完整版) - '
   '副本\\build\\亚马逊蓝图工具\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'C:\\Users\\<USER>\\Desktop\\授权最新文件\\新建文件夹\\新建文件夹 (完整版) - '
   '副本\\build\\亚马逊蓝图工具\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'C:\\Users\\<USER>\\Desktop\\授权最新文件\\新建文件夹\\新建文件夹 (完整版) - '
   '副本\\build\\亚马逊蓝图工具\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'C:\\Users\\<USER>\\Desktop\\授权最新文件\\新建文件夹\\新建文件夹 (完整版) - '
   '副本\\build\\亚马逊蓝图工具\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'C:\\Users\\<USER>\\Desktop\\授权最新文件\\新建文件夹\\新建文件夹 (完整版) - '
   '副本\\build\\亚马逊蓝图工具\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'C:\\Users\\<USER>\\Desktop\\授权最新文件\\新建文件夹\\新建文件夹 (完整版) - '
   '副本\\build\\亚马逊蓝图工具\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('pyi_rth_pywintypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pywintypes.py',
   'PYSOURCE'),
  ('pyi_rth_pythoncom',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pythoncom.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('license_client',
   'C:\\Users\\<USER>\\Desktop\\授权最新文件\\新建文件夹\\新建文件夹 (完整版) - 副本\\license_client.py',
   'PYSOURCE-2'),
  ('selenium\\webdriver\\common\\windows\\selenium-manager.exe',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\common\\windows\\selenium-manager.exe',
   'BINARY'),
  ('python310.dll',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\python310.dll',
   'BINARY'),
  ('numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'BINARY'),
  ('pywin32_system32\\pywintypes310.dll',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pywin32_system32\\pywintypes310.dll',
   'BINARY'),
  ('pywin32_system32\\pythoncom310.dll',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pywin32_system32\\pythoncom310.dll',
   'BINARY'),
  ('_multiprocessing.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('select.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('lxml\\etree.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\etree.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\_elementpath.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\_elementpath.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\charset_normalizer\\md__mypyc.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\charset_normalizer\\md.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\random\\mtrand.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\random\\_sfc64.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\random\\_philox.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\random\\_pcg64.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\random\\_mt19937.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\random\\bit_generator.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\random\\_generator.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\random\\_bounded_integers.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\random\\_common.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('yaml\\_yaml.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\yaml\\_yaml.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\_webp.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\_imagingtk.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_avif.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\_avif.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\_imagingcms.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\_imagingmath.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\_imaging.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\_cffi_backend.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('bcrypt\\_bcrypt.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\bcrypt\\_bcrypt.pyd',
   'EXTENSION'),
  ('lxml\\sax.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\sax.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\objectify.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\objectify.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\html\\diff.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\html\\diff.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\html\\_difflib.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\html\\_difflib.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\builder.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\builder.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('markupsafe\\_speedups.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\markupsafe\\_speedups.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashing.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\hashing.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\ops.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\groupby.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\groupby.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\interval.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\interval.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\offsets.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\tslibs\\offsets.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\nattype.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\tslibs\\nattype.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\period.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\tslibs\\period.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\tzconversion.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\tslibs\\tzconversion.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timezones.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\tslibs\\timezones.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslib.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\tslib.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\ccalendar.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\tslibs\\ccalendar.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\strptime.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\tslibs\\strptime.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\conversion.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\tslibs\\conversion.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timestamps.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\tslibs\\timestamps.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\np_datetime.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\tslibs\\np_datetime.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\fields.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\tslibs\\fields.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\dtypes.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\tslibs\\dtypes.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\parsing.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\tslibs\\parsing.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timedeltas.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\tslibs\\timedeltas.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\index.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\index.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashtable.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\hashtable.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\algos.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\algos.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\vectorized.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\tslibs\\vectorized.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\arrays.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\arrays.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sparse.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\sparse.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\internals.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\internals.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops_dispatch.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\ops_dispatch.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\indexing.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\indexing.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\join.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\join.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\indexers.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\window\\indexers.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\aggregations.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\window\\aggregations.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\reshape.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\reshape.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\properties.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\properties.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\writers.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\writers.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\parsers.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\parsers.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\json.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\json.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\missing.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\missing.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\pandas_datetime.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\pandas_datetime.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\pandas_parser.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\pandas_parser.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('_brotli.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\_brotli.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('nacl\\_sodium.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\nacl\\_sodium.pyd',
   'EXTENSION'),
  ('win32\\win32security.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32\\win32security.pyd',
   'EXTENSION'),
  ('win32\\_win32sysloader.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32\\_win32sysloader.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\lib.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\lib.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\base.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\tslibs\\base.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\testing.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\testing.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sas.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\sas.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\byteswap.cp310-win_amd64.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\_libs\\byteswap.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('win32\\win32api.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('Pythonwin\\win32ui.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\Pythonwin\\win32ui.pyd',
   'EXTENSION'),
  ('win32\\win32event.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32\\win32event.pyd',
   'EXTENSION'),
  ('win32\\win32trace.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32\\win32trace.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('_tkinter.pyd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_tkinter.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\VCRUNTIME140.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('libcrypto-1_1.dll',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('libssl-1_1.dll',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\libssl-1_1.dll',
   'BINARY'),
  ('libffi-7.dll',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\libffi-7.dll',
   'BINARY'),
  ('python3.dll',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\python3.dll',
   'BINARY'),
  ('pandas.libs\\msvcp140-1a0962f2a91a74c6d7136a768987a591.dll',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas.libs\\msvcp140-1a0962f2a91a74c6d7136a768987a591.dll',
   'BINARY'),
  ('sqlite3.dll',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\sqlite3.dll',
   'BINARY'),
  ('Pythonwin\\mfc140u.dll',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\Pythonwin\\mfc140u.dll',
   'BINARY'),
  ('tcl86t.dll',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\tcl86t.dll',
   'BINARY'),
  ('tk86t.dll',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\tk86t.dll',
   'BINARY'),
  ('fake_useragent\\data\\browsers.jsonl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fake_useragent\\data\\browsers.jsonl',
   'DATA'),
  ('fake_useragent\\py.typed',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fake_useragent\\py.typed',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\METADATA',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography-45.0.5.dist-info\\METADATA',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\licenses\\LICENSE',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography-45.0.5.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\licenses\\LICENSE.BSD',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography-45.0.5.dist-info\\licenses\\LICENSE.BSD',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\RECORD',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography-45.0.5.dist-info\\RECORD',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\licenses\\LICENSE.APACHE',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography-45.0.5.dist-info\\licenses\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\WHEEL',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography-45.0.5.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\INSTALLER',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\cryptography-45.0.5.dist-info\\INSTALLER',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'DATA'),
  ('lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'DATA'),
  ('selenium\\webdriver\\common\\devtools\\v137\\py.typed',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\common\\devtools\\v137\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\remote\\isDisplayed.js',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\remote\\isDisplayed.js',
   'DATA'),
  ('selenium\\webdriver\\remote\\getAttribute.js',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\remote\\getAttribute.js',
   'DATA'),
  ('selenium\\webdriver\\common\\mutation-listener.js',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\common\\mutation-listener.js',
   'DATA'),
  ('selenium\\webdriver\\common\\devtools\\v136\\py.typed',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\common\\devtools\\v136\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\common\\macos\\selenium-manager',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\common\\macos\\selenium-manager',
   'DATA'),
  ('selenium\\webdriver\\remote\\findElements.js',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\remote\\findElements.js',
   'DATA'),
  ('selenium\\webdriver\\common\\devtools\\v138\\py.typed',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\common\\devtools\\v138\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\common\\linux\\selenium-manager',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\common\\linux\\selenium-manager',
   'DATA'),
  ('selenium\\webdriver\\firefox\\webdriver_prefs.json',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\webdriver\\firefox\\webdriver_prefs.json',
   'DATA'),
  ('selenium\\py.typed',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\selenium\\py.typed',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_table.tpl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\formats\\templates\\html_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex.tpl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\formats\\templates\\latex.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\string.tpl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\formats\\templates\\string.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_style.tpl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\formats\\templates\\html_style.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_table.tpl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\formats\\templates\\latex_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html.tpl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pandas\\io\\formats\\templates\\html.tpl',
   'DATA'),
  ('dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('pytz\\zoneinfo\\Egypt',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Egypt',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dubai',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nuuk',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Calcutta',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belize',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Belize',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zaporozhye',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aden',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Omsk',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kolkata',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Saigon',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Canary',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('pytz\\zoneinfo\\Singapore',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\McMurdo',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Goose_Bay',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Knox_IN',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Gaborone',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Asuncion',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wallis',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Brazzaville',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuching',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dili',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rio_Branco',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atikokan',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('pytz\\zoneinfo\\leapseconds',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\leapseconds',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-5',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Hermosillo',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kamchatka',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bujumbura',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-12',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('pytz\\zoneinfo\\WET',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\WET',
   'DATA'),
  ('pytz\\zoneinfo\\Navajo',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Navajo',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Queensland',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Monrovia',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Syowa',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Vincent',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT0',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Pacific',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port_of_Spain',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Vancouver',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belem',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Belem',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaNorte',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coral_Harbour',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lome',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-9',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mendoza',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Simferopol',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\South',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\South',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tripoli',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('pytz\\zoneinfo\\Portugal',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Portugal',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('pytz\\zoneinfo\\Hongkong',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Hongkong',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ust-Nera',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boise',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Boise',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baghdad',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Freetown',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tallinn',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Anadyr',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Inuvik',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+8',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Toronto',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Efate',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Kerguelen',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dushanbe',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Dublin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Manila',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Matamoros',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mexico_City',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('pytz\\zoneinfo\\US\\East-Indiana',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson_Creek',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Troll',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-1',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\LHI',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Campo_Grande',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Metlakatla',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Prague',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaSur',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('pytz\\zoneinfo\\Universal',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Nicosia',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yakutat',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Samoa',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\NZ-CHAT',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baku',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Athens',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('pytz\\zoneinfo\\GMT-0',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Niamey',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\West',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Astrakhan',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dakar',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Riga',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qyzylorda',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santarem',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Whitehorse',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Urumqi',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Puerto_Rico',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Vostok',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-8',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Luanda',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kaliningrad',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Samara',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Atlantic',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fiji',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Mawson',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\Continental',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Damascus',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montreal',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bogota',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+3',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Danmarkshavn',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Aleutian',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kirov',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Panama',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Panama',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Curacao',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Maceio',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wake',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jerusalem',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Rangoon',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('pytz\\zoneinfo\\Kwajalein',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Brunei',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belfast',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Michigan',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Niue',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Helsinki',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Noronha',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guatemala',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Norfolk',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Barnaul',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Saipan',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Denver',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Denver',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Barbados',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('pytz\\zoneinfo\\Turkey',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Turkey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Menominee',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Shanghai',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Mountain',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Harare',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Wayne',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Warsaw',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Porto-Novo',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kiritimati',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tomsk',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\ACT',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Godthab',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kyiv',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kyiv',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cordoba',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anguilla',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Atyrau',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Broken_Hill',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashgabat',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Stanley',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tehran',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Gambier',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Sao_Tome',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Enderbury',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nairobi',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimphu',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vatican',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bucharest',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Jersey',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mazatlan',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lubumbashi',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Blanc-Sablon',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cancun',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Skopje',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Miquelon',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Christmas',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('pytz\\zoneinfo\\Poland',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Poland',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\North',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\North',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yangon',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('pytz\\zoneinfo\\PRC',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\PRC',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tashkent',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Martinique',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thule',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Thule',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atka',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Atka',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Amsterdam',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Indiana-Starke',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tarawa',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vladivostok',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Tasmania',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zagreb',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Edmonton',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('pytz\\zoneinfo\\PST8PDT',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\PST8PDT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Minsk',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('pytz\\zoneinfo\\tzdata.zi',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('pytz\\zoneinfo\\GMT',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Adelaide',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\General',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bamako',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Merida',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Merida',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayman',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dacca',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Famagusta',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('pytz\\zoneinfo\\EST',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\EST',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rosario',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Choibalsan',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Algiers',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\East',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\DeNoronha',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('pytz\\zoneinfo\\zonenow.tab',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\zonenow.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Greenwich',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Bougainville',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmera',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimbu',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Chagos',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montevideo',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Marigot',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Adak',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Adak',
   'DATA'),
  ('pytz\\zoneinfo\\GMT+0',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Salta',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kanton',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kanton',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Winnipeg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bangkok',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('pytz\\zoneinfo\\UCT',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\UTC',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Casey',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('pytz\\zoneinfo\\Factory',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Factory',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Johannesburg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Istanbul',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Casablanca',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Johns',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Los_Angeles',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Chisinau',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chatham',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Katmandu',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Madeira',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sarajevo',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yerevan',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Detroit',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\El_Aaiun',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('pytz\\zoneinfo\\MST7MDT',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\MST7MDT',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\La_Paz',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vilnius',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pohnpei',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tokyo',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mahe',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kathmandu',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bahrain',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santo_Domingo',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Auckland',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Stockholm',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nassau',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Comoro',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lower_Princes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ojinaga',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('pytz\\zoneinfo\\zone1970.tab',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\South_Pole',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Arizona',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santa_Isabel',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+10',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rankin_Inlet',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lima',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Lima',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+1',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('pytz\\zoneinfo\\Iceland',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Iceland',
   'DATA'),
  ('pytz\\zoneinfo\\Iran',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Iran',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Cocos',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qatar',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Magadan',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('pytz\\zoneinfo\\CST6CDT',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\CST6CDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lord_Howe',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\St_Helena',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Irkutsk',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashkhabad',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ensenada',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chongqing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\EasterIsland',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vienna',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Recife',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Recife',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Rome',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UCT',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Lucia',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tbilisi',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ndjamena',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chihuahua',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Samoa',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Palau',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guam',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yellowknife',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Rarotonga',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Karachi',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grenada',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Glace_Bay',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Accra',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Copenhagen',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Midway',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anchorage',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Zulu',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Manaus',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Riyadh',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tegucigalpa',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Knox',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kwajalein',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\iso3166.tab',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Victoria',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indianapolis',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Regina',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Regina',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Djibouti',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Sydney',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Abidjan',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Punta_Arenas',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Samarkand',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Brisbane',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('pytz\\zoneinfo\\CET',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\CET',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Bermuda',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tijuana',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Sakhalin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Shiprock',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Taipei',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\NSW',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Ponape',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boa_Vista',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Catamarca',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Hobart',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Juneau',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Funafuti',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guadeloupe',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nouakchott',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Timbuktu',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tortola',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montserrat',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Phoenix',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chicago',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guayaquil',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-13',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Maldives',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zurich',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Velho',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('pytz\\zoneinfo\\MET',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\MET',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Macquarie',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Singapore',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+5',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Eastern',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Halifax',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('pytz\\zoneinfo\\Jamaica',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tiraspol',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Greenwich',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Luxembourg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hong_Kong',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lindeman',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Douala',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtobe',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Newfoundland',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Truk',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('pytz\\zoneinfo\\GMT0',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Zulu',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Gibraltar',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Thomas',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jayapura',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-10',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Budapest',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Berlin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Scoresbysund',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('pytz\\zoneinfo\\EET',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\EET',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Podgorica',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Eucla',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Andorra',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Windhoek',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tongatapu',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chita',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Eirunepe',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Monaco',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Iqaluit',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayenne',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('pytz\\zoneinfo\\Israel',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Israel',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Reunion',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ouagadougou',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tunis',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Canberra',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faroe',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cuiaba',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Juba',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pontianak',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Nauru',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vientiane',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Banjul',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chuuk',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nipigon',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Madrid',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kinshasa',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Virgin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Lisbon',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\Acre',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Beirut',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuwait',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pyongyang',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('pytz\\zoneinfo\\America\\El_Salvador',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Colombo',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Louisville',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Muscat',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('pytz\\zoneinfo\\zone.tab',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\zone.tab',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hovd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Melbourne',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maputo',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bishkek',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bratislava',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Yancowinna',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\West',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sofia',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Universal',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Saratov',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-14',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kiev',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Central',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Mariehamn',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yakutsk',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-3',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\San_Marino',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Antigua',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-11',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lusaka',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('pytz\\zoneinfo\\Japan',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Japan',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Malabo',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rainy_River',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dominica',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('pytz\\zoneinfo\\ROC',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\ROC',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Darwin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Noumea',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('pytz\\zoneinfo\\Cuba',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Cuba',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nome',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nome',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('pytz\\zoneinfo\\Eire',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Eire',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Central',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\US\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Marquesas',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Oslo',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('pytz\\zoneinfo\\MST',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\MST',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Eastern',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mogadishu',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sitka',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Majuro',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Hawaii',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Gaza',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Aruba',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Paris',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Volgograd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qostanay',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jakarta',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+9',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kashgar',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Cairo',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('pytz\\zoneinfo\\GB-Eire',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\GB-Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novosibirsk',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Caracas',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tirane',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('pytz\\zoneinfo\\NZ',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\NZ',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kabul',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bangui',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Swift_Current',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Guernsey',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+6',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Nicosia',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macau',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Acre',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pitcairn',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port-au-Prince',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mauritius',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fortaleza',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kosrae',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Managua',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Managua',
   'DATA'),
  ('pytz\\zoneinfo\\Libya',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Libya',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Busingen',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Monterrey',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+7',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faeroe',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belgrade',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Araguaina',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+2',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sao_Paulo',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maseru',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Oral',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-6',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-4',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Havana',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Havana',
   'DATA'),
  ('pytz\\zoneinfo\\ROK',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\ROK',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Pangnirtung',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Paramaribo',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Apia',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grand_Turk',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Uzhgorod',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vaduz',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Palmer',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('pytz\\zoneinfo\\EST5EDT',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\EST5EDT',
   'DATA'),
  ('pytz\\zoneinfo\\GB',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\GB',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lagos',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Yap',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bissau',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Khandyga',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coyhaique',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Coyhaique',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mayotte',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('pytz\\zoneinfo\\W-SU',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\W-SU',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Seoul',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Istanbul',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ljubljana',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kralendijk',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Kitts',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Harbin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtau',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Honolulu',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Resolute',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Alaska',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mbabane',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Conakry',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Moscow',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Azores',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guyana',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\London',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\London',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Yukon',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macao',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+4',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ceuta',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thunder_Bay',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Moncton',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Mountain',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Antananarivo',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Malta',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kigali',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Amman',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Barthelemy',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Galapagos',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Almaty',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Rothera',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-2',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Easter',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-7',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Creston',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Creston',
   'DATA'),
  ('pytz\\zoneinfo\\HST',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\HST',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+0',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santiago',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hebron',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Davis',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+12',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cambridge_Bay',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Pacific',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Currie',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Makassar',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Costa_Rica',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-0',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Khartoum',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Perth',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Nelson',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Buenos_Aires',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fakaofo',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Libreville',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UTC',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chungking',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Johnston',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia_Banderas',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dhaka',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+11',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kampala',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Brussels',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tahiti',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jamaica',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Blantyre',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Saskatchewan',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmara',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('pytz\\zoneinfo\\America\\New_York',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\New_York',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jujuy',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pytz\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('certifi\\py.typed',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('certifi\\cacert.pem',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('nacl\\py.typed',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\nacl\\py.typed',
   'DATA'),
  ('_tk_data\\tearoff.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\tearoff.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ar_jo.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1252.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('_tcl_data\\msgs\\fa_ir.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('_tk_data\\msgs\\en.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('_tk_data\\dialog.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\dialog.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Eastern',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+7',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('_tcl_data\\tzdata\\CET',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\CET',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Canberra',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+4',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dushanbe',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('_tcl_data\\tzdata\\Jamaica',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('_tcl_data\\msgs\\es_ec.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Choibalsan',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('_tcl_data\\encoding\\macTurkish.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nassau',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Louisville',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Manila',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Matamoros',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thunder_Bay',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Buenos_Aires',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Syowa',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('_tk_data\\ttk\\menubutton.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faeroe',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vientiane',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Harbin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('_tk_data\\ttk\\notebook.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp1250.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macRomania.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Istanbul',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Merida',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Regina',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kiritimati',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Currie',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('_tcl_data\\msgs\\uk.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Catamarca',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('_tcl_data\\msgs\\hr.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Recife',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Luis',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montevideo',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Danmarkshavn',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Freetown',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UCT',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('_tcl_data\\encoding\\euc-kr.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\HST',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\HST',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Rarotonga',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Gaza',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qostanay',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Qostanay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guadeloupe',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Salta',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Tucuman',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('_tcl_data\\msgs\\lt.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9YDT',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Saratov',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-7',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('_tcl_data\\encoding\\tis-620.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('_tk_data\\images\\logoLarge.gif',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Goose_Bay',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Skopje',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-16.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jujuy',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cordoba',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Addis_Ababa',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('_tcl_data\\opt0.4\\pkgIndex.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('_tk_data\\button.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santa_Isabel',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Tell_City',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nome',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('_tcl_data\\tzdata\\Iran',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bamako',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson_Creek',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('_tcl_data\\encoding\\cns11643.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cns11643.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Funafuti',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cancun',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Creston',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Abidjan',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rainy_River',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Busingen',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Madeira',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tijuana',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belfast',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Galapagos',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guam',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Stanley',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kiev',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bishkek',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Shanghai',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('_tcl_data\\msgs\\fi.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp737.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Edmonton',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aden',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Budapest',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Harare',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Istanbul',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('_tk_data\\menu.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\menu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Queensland',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('_tk_data\\clrpick.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\clrpick.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Calcutta',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('_tk_data\\license.terms',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('_tk_data\\ttk\\scrollbar.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Blanc-Sablon',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('_tcl_data\\encoding\\euc-cn.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('_tcl_data\\msgs\\en_ph.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vincennes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('_tcl_data\\tzdata\\WET',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\WET',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+8',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lome',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('_tcl_data\\clock.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\clock.tcl',
   'DATA'),
  ('_tk_data\\pkgIndex.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Araguaina',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\DeNoronha',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Seoul',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('_tcl_data\\msgs\\ar.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ch.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Halifax',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macao',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Malta',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mahe',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kirov',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zaporozhye',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('_tk_data\\comdlg.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\comdlg.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Banjul',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulan_Bator',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('_tcl_data\\msgs\\he.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pitcairn',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Davis',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Whitehorse',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tallinn',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('_tk_data\\images\\tai-ku.gif',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('_tcl_data\\msgs\\en_be.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmera',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('_tcl_data\\tm.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tm.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Libreville',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('_tcl_data\\msgs\\it.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kigali',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Nauru',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('_tk_data\\ttk\\clamTheme.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-6',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('_tcl_data\\encoding\\jis0201.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('_tcl_data\\msgs\\mr.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rosario',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Douala',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Honolulu',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Riyadh',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Warsaw',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('_tcl_data\\encoding\\cp869.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo75.gif',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Noumea',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baku',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('_tcl_data\\msgs\\fa.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('_tcl_data\\msgs\\eu.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('_tcl_data\\msgs\\da.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chicago',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nuuk',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Nuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Uzhgorod',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Brunei',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Saigon',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('_tcl_data\\encoding\\big5.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('_tk_data\\msgs\\de.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rankin_Inlet',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Punta_Arenas',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Maldives',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-3.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4ADT',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Monticello',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-0',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vladivostok',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Saipan',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-5.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chongqing',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('_tcl_data\\msgs\\nn.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\New_York',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ho_Chi_Minh',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kamchatka',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\Acre',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Caracas',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ojinaga',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nairobi',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lubumbashi',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Winamac',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('_tk_data\\iconlist.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\General',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Scoresbysund',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('_tcl_data\\msgs\\zh_hk.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-14.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Volgograd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belize',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('_tcl_data\\word.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\word.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Atlantic',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Mountain',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('_tcl_data\\msgs\\en_ie.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('_tk_data\\msgs\\eo.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\GB-Eire',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qatar',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\NZ',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Norfolk',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\LHI',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('_tcl_data\\tzdata\\PST8PDT',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuala_Lumpur',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Metlakatla',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Pangnirtung',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Oslo',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+3',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Simferopol',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('_tcl_data\\msgs\\bn.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+12',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kanton',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Kanton',
   'DATA'),
  ('_tk_data\\choosedir.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo150.gif',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Amsterdam',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\West',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Midway',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('_tcl_data\\msgs\\es_pe.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\South_Pole',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('_tcl_data\\msgs\\ar_sy.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Timbuktu',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Mountain',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('_tcl_data\\msgs\\en_in.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('_tk_data\\ttk\\progress.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Reykjavik',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('_tcl_data\\tzdata\\EST5EDT',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuwait',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('_tk_data\\safetk.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\safetk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dacca',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tripoli',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Dublin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('_tcl_data\\package.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\package.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\La_Paz',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mexico_City',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vaduz',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ensenada',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Taipei',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indianapolis',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Samoa',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('_tcl_data\\encoding\\cp865.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tarawa',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('_tcl_data\\tzdata\\Eire',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('_tcl_data\\encoding\\macUkraine.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Nelson',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('_tcl_data\\encoding\\cp1256.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaNorte',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Conakry',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo175.gif',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Barthelemy',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('_tcl_data\\tzdata\\ROK',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-11',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dubai',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('_tcl_data\\tzdata\\Turkey',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashgabat',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('_tcl_data\\msgs\\zh.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-u.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('_tcl_data\\parray.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\parray.tcl',
   'DATA'),
  ('_tcl_data\\init.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\init.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atka',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('_tk_data\\ttk\\sizegrip.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Arizona',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Astrakhan',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Reunion',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Samarkand',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boa_Vista',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Melbourne',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Mendoza',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kabul',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('_tcl_data\\msgs\\gl.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sr.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1258.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('_tk_data\\icons.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\icons.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Eastern',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-6.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo200.gif',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data\\msgs\\el.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\msgs\\te.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Indianapolis',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mogadishu',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('_tcl_data\\msgs\\en_bw.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('_tcl_data\\encoding\\jis0208.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+11',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('_tcl_data\\msgs\\lv.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vienna',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Anadyr',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('_tk_data\\ttk\\combobox.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ca.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Mawson',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wallis',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+0',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Famagusta',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('_tcl_data\\msgs\\es_py.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-2',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Atyrau',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\El_Aaiun',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('_tcl_data\\msgs\\es_pa.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Copenhagen',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Stockholm',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('_tcl_data\\msgs\\gv.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Hermosillo',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('_tcl_data\\tzdata\\W-SU',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+10',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Makassar',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('_tcl_data\\history.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\history.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp850.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guyana',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\East',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Rangoon',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('_tcl_data\\msgs\\kw.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ro.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-4.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.3.tm',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8\\8.5\\tcltest-2.5.3.tm',
   'DATA'),
  ('_tcl_data\\encoding\\cp866.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-1',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('_tcl_data\\msgs\\en_ca.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ja.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-9.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('_tcl_data\\msgs\\th.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Mariehamn',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('_tcl_data\\http1.0\\pkgIndex.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-8',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nipigon',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaSur',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Miquelon',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\South_Georgia',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hebron',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Khandyga',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\Continental',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('_tcl_data\\msgs\\de_be.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sw.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('_tcl_data\\msgs\\te_in.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Yap',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kosrae',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Juneau',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('_tcl_data\\encoding\\macGreek.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Israel',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('_tcl_data\\tzdata\\Cuba',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('_tcl_data\\tzdata\\Hongkong',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Michigan',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('_tk_data\\ttk\\cursors.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('_tk_data\\images\\README',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtau',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bangui',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Krasnoyarsk',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Paramaribo',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('_tcl_data\\msgs\\be.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('_tcl_data\\msgs\\bg.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\UCT',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('_tcl_data\\msgs\\ga_ie.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('_tcl_data\\encoding\\ebcdic.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Eirunepe',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\McMurdo',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bogota',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('_tk_data\\ttk\\button.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Cocos',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ-CHAT',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jayapura',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('_tcl_data\\msgs\\es_co.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\MET',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\MET',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dhaka',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Nicosia',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('_tcl_data\\msgs\\ga.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chihuahua',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo.eps',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\HST10',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Srednekolymsk',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('_tcl_data\\msgs\\kok.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-2.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific-New',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Pacific-New',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dili',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mazatlan',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Costa_Rica',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('_tcl_data\\msgs\\eu_es.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('_tcl_data\\msgs\\pt.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Asuncion',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('_tk_data\\tkfbox.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\tkfbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Johannesburg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('_tk_data\\ttk\\classicTheme.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\CST6CDT',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('_tk_data\\msgs\\en_gb.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bujumbura',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Windhoek',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yakutsk',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('_tcl_data\\encoding\\symbol.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Kerguelen',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vevay',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('_tcl_data\\msgs\\af_za.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novosibirsk',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Antananarivo',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.5.tm',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8\\8.6\\http-2.9.5.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayenne',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Acre',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\Universal',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('_tcl_data\\msgs\\ms_my.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Sakhalin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('_tk_data\\msgs\\el.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1251.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('_tk_data\\ttk\\xpTheme.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tegucigalpa',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Vancouver',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Barnaul',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('_tcl_data\\tclIndex',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tclIndex',
   'DATA'),
  ('_tk_data\\ttk\\winTheme.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-4',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pago_Pago',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('_tcl_data\\encoding\\cp950.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Almaty',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Muscat',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Maceio',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('_tcl_data\\encoding\\macCentEuro.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Panama',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bissau',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('_tcl_data\\msgs\\ar_in.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp949.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp1255.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Hobart',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('_tk_data\\mkpsenc.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\mkpsenc.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\macRoman.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-10',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Blantyre',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pohnpei',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pyongyang',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boise',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santiago',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('_tk_data\\ttk\\fonts.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('_tk_data\\images\\logo64.gif',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-14',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dar_es_Salaam',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('_tcl_data\\msgs\\es_cl.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Johns',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('_tcl_data\\safe.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\safe.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Rothera',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Marengo',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Easter',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('_tcl_data\\encoding\\macCroatian.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Christmas',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashkhabad',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('_tk_data\\bgerror.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\bgerror.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lagos',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Darwin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Andorra',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('_tk_data\\msgs\\sv.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Virgin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Puerto_Rico',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('_tcl_data\\msgs\\mk.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Brussels',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yerevan',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kralendijk',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('_tk_data\\msgs\\it.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Central',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Noronha',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-kr.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macJapan.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Los_Angeles',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Niue',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('_tcl_data\\tzdata\\Egypt',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Johnston',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('_tcl_data\\encoding\\cp857.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('_tk_data\\msgs\\pt.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Karachi',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('_tcl_data\\encoding\\cp860.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('_tk_data\\megawidget.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Jersey',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('_tcl_data\\encoding\\cp437.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp855.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kashgar',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Antigua',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Velho',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('_tcl_data\\encoding\\ksc5601.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Navajo',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('_tcl_data\\tzdata\\Singapore',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Amman',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grenada',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Juba',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tomsk',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bratislava',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('_tk_data\\msgs\\ru.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maseru',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+6',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santarem',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jerusalem',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lindeman',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Cordoba',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Magadan',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ndjamena',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('_tcl_data\\encoding\\macThai.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp1257.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('_tk_data\\focus.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\focus.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Gaborone',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('_tcl_data\\msgs\\es_ar.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('_tk_data\\ttk\\vistaTheme.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\zh_sg.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Tasmania',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('_tcl_data\\msgs\\en_gb.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Palmer',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Barbados',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Samoa',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+5',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tunis',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Bougainville',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('_tcl_data\\http1.0\\http.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Menominee',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('_tcl_data\\tzdata\\Portugal',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Brisbane',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('_tcl_data\\tzdata\\UTC',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('_tcl_data\\encoding\\cp862.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Indiana-Starke',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('_tk_data\\images\\logo100.gif',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-3',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-5',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hong_Kong',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kinshasa',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+2',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Vincent',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Athens',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Perth',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tahiti',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('_tk_data\\fontchooser.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Troll',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Yukon',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Shiprock',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Moscow',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\New_Salem',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('_tcl_data\\encoding\\gb1988.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('_tcl_data\\msgs\\tr.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_do.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Buenos_Aires',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-13',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('_tcl_data\\msgs\\nl.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chuuk',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zurich',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Riga',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novokuznetsk',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Adak',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulaanbaatar',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Catamarca',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Comoro',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faroe',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuching',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('_tcl_data\\msgs\\mt.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Nicosia',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('_tcl_data\\msgs\\es_sv.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Accra',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('_tk_data\\text.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\text.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+9',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Pacific',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Iqaluit',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Macquarie',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('_tk_data\\msgs\\fr.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_au.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\ComodRivadavia',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('_tcl_data\\msgs\\en_za.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('_tcl_data\\encoding\\dingbats.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('_tcl_data\\msgs\\hi.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('_tk_data\\images\\logoMed.gif',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Efate',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jakarta',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('_tcl_data\\encoding\\macCyrillic.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yellowknife',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bucharest',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('_tcl_data\\encoding\\cp775.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Phoenix',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Gambier',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-r.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chungking',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('_tcl_data\\tzdata\\PRC',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('_tcl_data\\tzdata\\EET',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\EET',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\North',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Madrid',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Glace_Bay',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Algiers',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('_tcl_data\\msgs\\af.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('_tcl_data\\msgs\\id.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Cape_Verde',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('_tcl_data\\msgs\\fa_in.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Louisville',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Kitts',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ujung_Pandang',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guadalcanal',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Greenwich',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\Greenwich',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT+0',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia_Banderas',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Marigot',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('_tcl_data\\msgs\\kl.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-8.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('_tk_data\\ttk\\aquaTheme.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bangkok',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('_tcl_data\\msgs\\fo.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('_tcl_data\\encoding\\jis0212.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-12',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('_tcl_data\\msgs\\vi.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Manaus',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Palau',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montserrat',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guatemala',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('_tcl_data\\msgs\\hu.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Zulu',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmara',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kathmandu',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Campo_Grande',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Chisinau',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('_tcl_data\\tzdata\\MST',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\MST',
   'DATA'),
  ('_tk_data\\console.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\console.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ko.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fiji',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('_tcl_data\\msgs\\is.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ljubljana',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Inuvik',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('_tcl_data\\tzdata\\Poland',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('_tcl_data\\msgs\\zh_cn.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\msgs\\pl.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tk_data\\msgs\\hu.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yekaterinburg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Urumqi',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Juan',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Sao_Tome',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('_tcl_data\\encoding\\cp1254.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('_tcl_data\\msgs\\de.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ust-Nera',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('_tcl_data\\encoding\\cp936.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thule',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('_tcl_data\\msgs\\nb.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('_tk_data\\obsolete.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lima',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Aleutian',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wake',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ulyanovsk',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Wayne',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('_tcl_data\\msgs\\fo_fo.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('_tk_data\\xmfbox.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\xmfbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Japan',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('_tcl_data\\encoding\\cp874.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grand_Turk',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sarajevo',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santo_Domingo',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macau',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Singapore',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Brazzaville',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Moncton',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('_tcl_data\\tzdata\\Kwajalein',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Broken_Hill',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mayotte',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Managua',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atikokan',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('_tcl_data\\msgs\\en_zw.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Rome',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Canary',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('_tcl_data\\encoding\\cp861.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('_tk_data\\ttk\\scale.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Podgorica',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Ushuaia',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('_tk_data\\ttk\\treeview.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tiraspol',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anguilla',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Enderbury',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5EDT',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Guernsey',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Chagos',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\San_Marino',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7MDT',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vatican',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('_tk_data\\listbox.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\listbox.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\hi_in.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port_of_Spain',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tokyo',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belem',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('_tk_data\\ttk\\panedwindow.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-jp.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('_tcl_data\\encoding\\shiftjis.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vilnius',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-9',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('_tcl_data\\encoding\\macDingbats.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Khartoum',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('_tcl_data\\msgs\\es_hn.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('_tcl_data\\msgs\\nl_be.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-11.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-11.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\East-Indiana',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Casablanca',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT0',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Port_Moresby',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('_tk_data\\ttk\\altTheme.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Beirut',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.18.tm',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8\\8.4\\platform-1.0.18.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Vostok',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dakar',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('_tcl_data\\msgs\\en_hk.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('_tk_data\\ttk\\spinbox.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Knox_IN',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tel_Aviv',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tashkent',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guayaquil',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('_tcl_data\\encoding\\cp863.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6CDT',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tbilisi',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Ponape',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Victoria',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Resolute',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('_tcl_data\\msgs\\pt_br.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yakutat',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kwajalein',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Denver',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sitka',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('_tcl_data\\encoding\\cp932.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_bo.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Marquesas',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Hawaii',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Zulu',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pontianak',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('_tcl_data\\msgs\\gv_gb.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sk.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\London',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+1',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fortaleza',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baghdad',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cuiaba',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('_tcl_data\\msgs\\ru_ua.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('_tk_data\\msgs\\nl.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chatham',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('_tk_data\\images\\logo.eps',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('_tcl_data\\msgs\\zh_tw.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ru.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\South',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Toronto',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Coral_Harbour',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Detroit',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-7.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Apia',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('_tcl_data\\msgs\\es_cr.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bahrain',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Jan_Mayen',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('_tcl_data\\msgs\\ta.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayman',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chita',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('_tcl_data\\msgs\\sl.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Luxembourg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('_tcl_data\\auto.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\auto.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Phnom_Penh',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('_tcl_data\\msgs\\id_id.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Porto-Novo',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Azores',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\ACT',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Colombo',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('_tcl_data\\msgs\\fr.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Alaska',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nouakchott',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sofia',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Winnipeg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('_tcl_data\\msgs\\es_ve.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-1.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('_tcl_data\\encoding\\ascii.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('_tcl_data\\msgs\\mr_in.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sh.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hovd',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('_tk_data\\msgs\\pl.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\kl_gl.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('_tk_data\\panedwindow.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('_tk_data\\ttk\\entry.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Eucla',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lord_Howe',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Thomas',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montreal',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Yancowinna',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kampala',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Jujuy',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('_tcl_data\\msgs\\ar_lb.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp864.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Katmandu',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('_tcl_data\\encoding\\euc-jp.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('_tk_data\\scale.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\scale.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\MST7MDT',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\ROC',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Aruba',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Havana',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belgrade',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Center',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('_tcl_data\\msgs\\kw_gb.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anchorage',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('_tcl_data\\msgs\\ko_kr.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp852.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Libya',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\St_Helena',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Beulah',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('_tcl_data\\msgs\\es.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\msgs\\et.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('_tk_data\\msgbox.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\msgbox.tcl',
   'DATA'),
  ('_tk_data\\msgs\\es.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Minsk',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Truk',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('_tcl_data\\encoding\\gb12345.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('_tcl_data\\opt0.4\\optparse.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kolkata',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Samara',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('_tcl_data\\encoding\\macIceland.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tehran',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('_tcl_data\\tzdata\\EST',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\EST',
   'DATA'),
  ('_tk_data\\palette.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\palette.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-13.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('_tcl_data\\msgs\\ca.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yangon',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Omsk',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('_tcl_data\\tzdata\\GB',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\GB',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kaliningrad',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('_tcl_data\\msgs\\sq.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lusaka',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('_tcl_data\\msgs\\ms.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Luanda',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Knox',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port-au-Prince',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Lucia',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('_tcl_data\\msgs\\es_mx.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Sydney',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Godthab',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maputo',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('_tk_data\\scrlbar.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\scrlbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tirane',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tortola',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Helsinki',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Isle_of_Man',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Saskatchewan',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('_tk_data\\tk.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\tk.tcl',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('_tcl_data\\msgs\\eo.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tk_data\\msgs\\da.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimphu',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('_tk_data\\ttk\\utils.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('_tk_data\\unsupported.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Berlin',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Central',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-15.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-10.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Paris',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Djibouti',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('_tk_data\\ttk\\ttk.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8PDT',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qyzylorda',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT-0',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Lisbon',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Petersburg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('_tcl_data\\msgs\\kok_in.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('_tk_data\\entry.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\entry.tcl',
   'DATA'),
  ('_tk_data\\optMenu.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\optMenu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Arctic\\Longyearbyen',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('_tcl_data\\msgs\\sv.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\EasterIsland',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('_tcl_data\\tzdata\\Iceland',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('_tcl_data\\msgs\\fr_be.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Curacao',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('_tcl_data\\msgs\\bn_in.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jamaica',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('_tcl_data\\encoding\\cp1253.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Bermuda',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Auckland',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cambridge_Bay',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\La_Rioja',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo100.gif',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Irkutsk',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('_tcl_data\\msgs\\es_uy.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Prague',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Universal',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimbu',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dominica',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Gibraltar',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Monaco',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ceuta',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Malabo',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('_tk_data\\msgs\\cs.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_gt.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ta_in.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\El_Salvador',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('_tcl_data\\msgs\\cs.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lower_Princes',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Majuro',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT0',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtobe',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('_tcl_data\\msgs\\it_ch.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mendoza',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('_tcl_data\\msgs\\es_ni.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Cairo',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\East-Saskatchewan',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Canada\\East-Saskatchewan',
   'DATA'),
  ('_tk_data\\tclIndex',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fakaofo',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Casey',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Monterrey',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('_tcl_data\\msgs\\de_at.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Swift_Current',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mbabane',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('_tcl_data\\msgs\\en_sg.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('_tcl_data\\msgs\\gl_es.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ouagadougou',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('_tcl_data\\msgs\\en_nz.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_pr.msg',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('_tk_data\\ttk\\defaults.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\DumontDUrville',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tongatapu',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Newfoundland',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zagreb',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sao_Paulo',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\West',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Monrovia',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Martinique',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('_tk_data\\spinbox.tcl',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tk8.6\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Niamey',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Adelaide',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mauritius',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\NSW',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Damascus',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UTC',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rio_Branco',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312-raw.enc',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Oral',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('numpy-2.2.6.dist-info\\LICENSE.txt',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy-2.2.6.dist-info\\LICENSE.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\WHEEL',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\WHEEL',
   'DATA'),
  ('numpy-2.2.6.dist-info\\DELVEWHEEL',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy-2.2.6.dist-info\\DELVEWHEEL',
   'DATA'),
  ('numpy-2.2.6.dist-info\\RECORD',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy-2.2.6.dist-info\\RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\METADATA',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\METADATA',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\RECORD',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\RECORD',
   'DATA'),
  ('fake_useragent-2.2.0.dist-info\\top_level.txt',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fake_useragent-2.2.0.dist-info\\top_level.txt',
   'DATA'),
  ('numpy-2.2.6.dist-info\\METADATA',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy-2.2.6.dist-info\\METADATA',
   'DATA'),
  ('fake_useragent-2.2.0.dist-info\\licenses\\LICENSE',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fake_useragent-2.2.0.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('fake_useragent-2.2.0.dist-info\\licenses\\AUTHORS',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fake_useragent-2.2.0.dist-info\\licenses\\AUTHORS',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('fake_useragent-2.2.0.dist-info\\RECORD',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fake_useragent-2.2.0.dist-info\\RECORD',
   'DATA'),
  ('numpy-2.2.6.dist-info\\entry_points.txt',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy-2.2.6.dist-info\\entry_points.txt',
   'DATA'),
  ('fake_useragent-2.2.0.dist-info\\METADATA',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fake_useragent-2.2.0.dist-info\\METADATA',
   'DATA'),
  ('fake_useragent-2.2.0.dist-info\\WHEEL',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fake_useragent-2.2.0.dist-info\\WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('numpy-2.2.6.dist-info\\WHEEL',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy-2.2.6.dist-info\\WHEEL',
   'DATA'),
  ('numpy-2.2.6.dist-info\\INSTALLER',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\numpy-2.2.6.dist-info\\INSTALLER',
   'DATA'),
  ('fake_useragent-2.2.0.dist-info\\INSTALLER',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fake_useragent-2.2.0.dist-info\\INSTALLER',
   'DATA'),
  ('fake_useragent-2.2.0.dist-info\\REQUESTED',
   'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fake_useragent-2.2.0.dist-info\\REQUESTED',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\授权最新文件\\新建文件夹\\新建文件夹 (完整版) - '
   '副本\\build\\亚马逊蓝图工具\\base_library.zip',
   'DATA')],
 'python310.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
