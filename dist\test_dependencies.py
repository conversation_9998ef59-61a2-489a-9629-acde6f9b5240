#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""exe依赖测试脚本"""

import sys
import traceback

def test_import(module_name):
    try:
        __import__(module_name)
        return True, None
    except Exception as e:
        return False, str(e)

def main():
    print("=" * 50)
    print("🧪 exe依赖测试")
    print("=" * 50)

    test_modules = [
        'fake_useragent', 'openpyxl', 'pandas', 'requests',
        'selenium', 'bs4', 'lxml', 'cryptography', 'PIL'
    ]

    passed = 0
    total = len(test_modules)

    for module in test_modules:
        success, error = test_import(module)
        if success:
            print(f"✅ {module} - 导入成功")
            passed += 1
        else:
            print(f"❌ {module} - 导入失败: {error}")

    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过 ({passed/total*100:.1f}%)")

    if passed == total:
        print("🎉 所有依赖测试通过！")
    else:
        print("⚠️ 部分依赖测试失败")

    return passed == total

if __name__ == "__main__":
    main()
