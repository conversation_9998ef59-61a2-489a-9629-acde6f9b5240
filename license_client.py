import os
import json
import sys
import tkinter as tk
from tkinter import ttk, messagebox
import requests
import subprocess
import platform
import socket
import zipfile  # 添加用于解压缩扩展的模块
import io  # 用于内存文件操作
import base64  # 用于图标编码和解密
import importlib.util  # 用于从内存加载模块
import types  # 用于创建模块对象
import tempfile  # 用于创建临时文件
import traceback  # 用于详细错误信息
import logging  # 用于日志记录
import time  # 用于时间戳
import threading  # 用于并发执行
from pathlib import Path  # 用于跨平台路径处理
import configparser
import hashlib
import webbrowser
from datetime import datetime, timedelta
import uuid
import secrets  # 用于安全随机数生成

# 导入自动更新模块
try:
    from auto_updater import check_and_update_silent as check_and_update
except ImportError:
    def check_and_update(*args, **kwargs):
        return False

# 尝试导入cryptography相关库
try:
    from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
    from cryptography.hazmat.backends import default_backend
    from cryptography.hazmat.primitives import hashes
    from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
    from cryptography.fernet import Fernet
    CRYPTOGRAPHY_AVAILABLE = True
except ImportError:
    CRYPTOGRAPHY_AVAILABLE = False

# 只在Windows平台上导入wmi模块
if platform.system() == "Windows":
    try:
        import wmi
    except ImportError:
        # 如果不存在wmi模块，处理程序后面会跳过相关功能
        pass

# 修改日志配置，仅使用文件处理器而不输出到控制台
logger = logging.getLogger("amazon_client")
logger.setLevel(logging.INFO)  # 将日志级别改回INFO（从DEBUG降低详细程度）

# 添加日志文件处理器
log_file = os.path.join(os.path.expanduser("~"), "amazon_client.log")
file_handler = logging.FileHandler(log_file, encoding='utf-8')
file_handler.setLevel(logging.INFO)

# 设置日志格式
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
file_handler.setFormatter(formatter)

# 添加处理器到日志记录器
logger.addHandler(file_handler)

# 确保不会输出到控制台
logger.propagate = False

logger.info("=== 客户端启动 ===")
logger.info(f"日志文件路径: {log_file}")

# 定义单实例锁使用的端口号（全局变量）
SINGLE_INSTANCE_PORT = 45673

# 添加单实例检测机制
def ensure_single_instance():
    """确保程序只有一个实例运行"""
    try:
        # 创建一个全局唯一名的TCP套接字
        global single_instance_socket
        single_instance_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        
        # 使用一个特定端口，确保只有一个实例
        port = SINGLE_INSTANCE_PORT
        
        # 尝试绑定到这个端口
        single_instance_socket.bind(('localhost', port))
        single_instance_socket.listen(1)
        
        # 设置一个线程接受连接
        def accept_connections():
            while True:
                try:
                    conn, addr = single_instance_socket.accept()
                    conn.close()
                except:
                    break
        
        accept_thread = threading.Thread(target=accept_connections)
        accept_thread.daemon = True
        accept_thread.start()
        
        logger.info("成功获得单实例锁")
        return True
    except socket.error:
        # 如果端口已被占用，说明已有一个实例在运行
        logger.warning("程序已经在运行，将尝试激活现有窗口")
        
        try:
            # 尝试连接到现有实例通知它显示窗口
            client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            client_socket.connect(('localhost', SINGLE_INSTANCE_PORT))
            client_socket.close()
        except:
            pass
        
        # 在Windows上尝试激活现有窗口
        if platform.system() == "Windows":
            try:
                import ctypes
                # 定义FindWindow函数
                FindWindow = ctypes.windll.user32.FindWindowW
                # 定义ShowWindow函数
                ShowWindow = ctypes.windll.user32.ShowWindow
                # 常量定义
                SW_RESTORE = 9
                
                # 查找窗口（按标题）
                hwnd = FindWindow(None, "亚马逊蓝图工具")
                if hwnd != 0:
                    # 激活窗口
                    ShowWindow(hwnd, SW_RESTORE)
                    # 设置为前台窗口
                    ctypes.windll.user32.SetForegroundWindow(hwnd)
                    logger.info("已激活现有窗口")
            except Exception as e:
                logger.error(f"激活窗口失败: {str(e)}")
        
        return False

# 尝试获取单一实例锁，如果已经有实例在运行，则退出
is_first_instance = ensure_single_instance()

# 修改导入部分的AmazonUITheme处理逻辑，增加兼容性处理

try:
    from ui_theme import AmazonUITheme
    # 尝试添加_client属性，如果它不存在
    if not hasattr(AmazonUITheme, '_client'):
        setattr(AmazonUITheme, '_client', None)
    # 尝试添加set_client方法，如果它不存在
    if not hasattr(AmazonUITheme, 'set_client'):
        @classmethod
        def set_client_method(cls, client):
            cls._client = client
        setattr(AmazonUITheme, 'set_client', set_client_method)
except ImportError:
    # 如果找不到主题模块，定义简化版的AmazonUITheme
    class AmazonUITheme:
        _client = None
        _initialized = False
        
        @classmethod
        def set_client(cls, client):
            """设置客户端实例，用于获取临时目录路径"""
            cls._client = client
            
        @classmethod
        def setup_window(cls, root, title, size="800x600", resizable=(True, True), icon=True):
            root.title(title)
            root.geometry(size)
            root.resizable(resizable[0], resizable[1])
            
            # 尝试设置图标
            if icon:
                icon_path = cls.get_image_path()
                if icon_path and os.path.exists(icon_path):
                    try:
                        root.iconbitmap(icon_path)
                    except Exception:
                        pass
            
            return root
            
        @classmethod
        def get_image_path(cls):
            # 首先尝试从客户端的应用数据目录获取图标
            if cls._client and hasattr(cls._client, 'data_dir') and os.path.exists(cls._client.data_dir):
                data_icon_path = os.path.join(cls._client.data_dir, "icon.ico")
                if os.path.exists(data_icon_path):
                    return data_icon_path
            
            # 如果没有客户端实例或应用数据目录中没有图标，使用基础目录
            if getattr(sys, 'frozen', False):
                # 打包环境下，优先检查可执行文件所在目录
                base_dir = os.path.dirname(sys.executable)
                icon_path = os.path.join(base_dir, "icon.ico")
                if os.path.exists(icon_path):
                    return icon_path
                    
                # 如果可执行文件目录没有图标，检查临时目录提取的资源
                import tempfile
                temp_dir = tempfile._get_default_tempdir()
                temp_icon = os.path.join(temp_dir, "_MEI", "icon.ico")
                if os.path.exists(temp_icon):
                    return temp_icon
            else:
                # 非打包环境
                base_dir = os.path.dirname(os.path.abspath(__file__))
                icon_path = os.path.join(base_dir, "icon.ico")
                if os.path.exists(icon_path):
                    return icon_path
            
            # 最后尝试当前目录
            if os.path.exists("icon.ico"):
                return "icon.ico"
                
            return None

class SimpleClient:
    def check_and_install_dependencies(self, required_packages=None, force_check=False, status_callback=None):
        """检查并安装程序所需的依赖库，使用缓存机制减少检查频率
        
        Args:
            required_packages: 需要检查的依赖包字典，格式为 {包名: 导入名}
            force_check: 是否强制检查（忽略缓存）
            status_callback: 状态更新回调函数，接收状态消息字符串
        
        Returns:
            bool: 安装是否成功
        """
        # 使用回调函数更新状态
        def update_status(message):
            if status_callback:
                status_callback(message)
            self.silent_log(message)
            
        # 初始化默认依赖包
        if required_packages is None:
            required_packages = {
                'selenium': 'selenium',
                'beautifulsoup4': 'bs4',
                'pandas': 'pandas',
                'openpyxl': 'openpyxl',  # Excel读写支持
                'xlsxwriter': 'xlsxwriter',  # Excel写入支持
                'requests': 'requests',
                'webdriver_manager': 'webdriver_manager',
                'psutil': 'psutil',
                'amazoncaptcha': 'amazoncaptcha',
                'cryptography': 'cryptography',  # 加密库依赖
                'fake_useragent': 'fake_useragent',  # 用户代理库依赖
                'lxml': 'lxml',  # XML/HTML解析
                'pillow': 'PIL'  # 图像处理
            }
        
        # 如果不是强制检查，且有有效的依赖缓存，则跳过检查
        current_time = datetime.now()
        
        if not force_check and self.dependencies_cache and 'last_check' in self.dependencies_cache:
            # 获取上次检查时间和缓存的依赖状态
            last_check = datetime.fromisoformat(self.dependencies_cache['last_check'])
            cached_status = self.dependencies_cache.get('status', {})
            
            # 检查所有需要的包是否都已缓存为已安装状态
            all_installed = True
            for package_name in required_packages:
                if package_name not in cached_status or not cached_status[package_name]:
                    all_installed = False
                    break
            
            # 如果上次检查时间在7天内，且所有包都已安装，直接返回成功
            if all_installed and (current_time - last_check) < timedelta(days=7):
                update_status("使用缓存的依赖检查结果，跳过依赖检查")
                return True
        
        update_status("开始检查依赖组件...")
        
        # 开始检查依赖
        missing_packages = []
        packages_status = {}
        
        for i, (package_name, import_name) in enumerate(required_packages.items()):
            update_status(f"检查组件: {package_name}...")
            try:
                import importlib
                importlib.import_module(import_name)
                packages_status[package_name] = True
                update_status(f"组件 {package_name} 检查通过")
            except ImportError:
                missing_packages.append(package_name)
                packages_status[package_name] = False
                update_status(f"组件 {package_name} 缺失，将安装")
        
        # 安装缺失的包
        if missing_packages:
            try:
                import subprocess
                import sys
                
                update_status(f"需要安装 {len(missing_packages)} 个依赖组件...")
                
                for i, package in enumerate(missing_packages):
                    update_status(f"正在安装 {package}... ({i+1}/{len(missing_packages)})")
                    subprocess.check_call(
                        [sys.executable, "-m", "pip", "install", package],
                        stdout=subprocess.DEVNULL,
                        stderr=subprocess.DEVNULL
                    )
                    # 安装成功后更新状态
                    packages_status[package] = True
                
                update_status("所有依赖组件安装完成")
            except Exception as e:
                update_status(f"安装依赖组件时出错: {str(e)}")
                # 注意：不更新缓存，因为安装失败
                return False
        else:
            update_status("所有依赖组件已正确安装")
        
        # 更新依赖缓存
        self.dependencies_cache = {
            'last_check': current_time.isoformat(),
            'status': packages_status
        }
        
        # 保存缓存到文件
        self.save_dependencies_cache()
        update_status("依赖检查完成")
        
        return True
    
    def safe_file_operation(self, operation_type, file_path, data=None, mode='r', encoding=None):
        """安全执行文件操作，处理异常
        
        Args:
            operation_type: 操作类型，'read'、'write'或'check'
            file_path: 文件路径
            data: 写入的数据（仅在写入时需要）
            mode: 文件打开模式
            encoding: 文件编码（默认为None，二进制模式不需要）
        
        Returns:
            根据操作类型不同返回不同的值:
            - 'read': 返回文件内容或None（失败时）
            - 'write': 返回True或False（成功/失败）
            - 'check': 返回True或False（文件存在/不存在）
        """
        try:
            if operation_type == 'check':
                return os.path.exists(file_path)
            
            if operation_type == 'read':
                with open(file_path, mode, encoding=encoding) as f:
                    return f.read()
            
            if operation_type == 'write':
                with open(file_path, mode, encoding=encoding) as f:
                    f.write(data)
                return True
            
            return False  # 未知操作类型
        except Exception as e:
            # 记录错误，但不中断程序执行
            operation_name = {"read": "读取", "write": "写入", "check": "检查"}
            logger.debug(f"文件{operation_name.get(operation_type, '操作')}失败 ({file_path}): {str(e)}")
            return None if operation_type == 'read' else False
    
    def load_dependencies_cache(self):
        """从文件加载依赖缓存"""
        cache_file = os.path.join(os.path.expanduser("~"), ".amazon_dependencies_cache")
        json_data = self.safe_file_operation('read', cache_file, mode='r', encoding='utf-8')
        if json_data:
            try:
                return json.loads(json_data)
            except Exception as e:
                logger.debug(f"依赖缓存JSON解析失败: {str(e)}")
        return {}
    
    def save_dependencies_cache(self):
        """保存依赖缓存到文件"""
        cache_file = os.path.join(os.path.expanduser("~"), ".amazon_dependencies_cache")
        self.safe_file_operation('write', cache_file, json.dumps(self.dependencies_cache), mode='w', encoding='utf-8')
    
    def __init__(self):
        logger.info("初始化客户端")
        # 服务器地址
        self.server_url = "http://198.23.135.176:5000"
        # 本地许可证文件
        self.license_file = os.path.join(os.path.expanduser("~"), ".amazon_price_license")
        # 许可证校验和文件
        self.license_checksum_file = os.path.join(os.path.expanduser("~"), ".amazon_license_checksum")
        # 可用程序列表
        self.available_programs = {
            "采集": "采集8.py.encrypted",
            "筛品": "筛品终极版1.py.encrypted",
            "价格": "历史价格7.py.encrypted",
            "专利": "专利1.py.encrypted"
        }
        
        # 初始化依赖缓存
        self.dependencies_cache = self.load_dependencies_cache()
        
        # 确定工作目录
        if getattr(sys, 'frozen', False):
            self.base_dir = os.path.dirname(sys.executable)
        else:
            self.base_dir = os.path.dirname(os.path.abspath(__file__))
        
        logger.info(f"工作目录: {self.base_dir}")
        
        # 设置固定的应用数据目录（用于存储图标、扩展等资源）
        self.data_dir = self.get_app_data_dir()
        logger.info(f"应用数据目录: {self.data_dir}")
        
        # 临时程序目录（用于安全存储程序文件）
        self.temp_dir = None
        
        # 内存存储程序代码的字典
        self.program_code = {}
        # 内存存储图标数据
        self.icon_data = None
        # 内存存储扩展文件
        self.extension_data = None
        # 正在运行的程序线程
        self.running_threads = {}
        # 浏览器进程跟踪
        self.browser_processes = {}
        
        # 执行环境完整性检查
        self.check_environment_integrity()
            
        # 初始化时自动检查图标
        self.check_and_download_icon()
        
        logger.info("客户端初始化完成")
    
    def get_app_data_dir(self):
        """获取应用数据目录，并确保目录存在"""
        # 确定用户数据目录
        if platform.system() == "Windows":
            app_data_dir = os.path.join(os.environ.get('LOCALAPPDATA', os.path.expanduser("~")), "AmazonClient")
        elif platform.system() == "Darwin":  # macOS
            app_data_dir = os.path.join(os.path.expanduser("~"), "Library", "Application Support", "AmazonClient")
        else:  # Linux和其他系统
            app_data_dir = os.path.join(os.path.expanduser("~"), ".local", "share", "AmazonClient")
        
        # 确保目录存在
        try:
            if not os.path.exists(app_data_dir):
                os.makedirs(app_data_dir)
                logger.info(f"创建应用数据目录: {app_data_dir}")
        except Exception as e:
            logger.error(f"创建应用数据目录失败: {str(e)}")
            # 如果无法创建数据目录，则使用base_dir
            app_data_dir = self.base_dir
            logger.info(f"使用工作目录作为应用数据目录: {app_data_dir}")
        
        return app_data_dir
    
    def __del__(self):
        # 清理临时目录
        self.cleanup_temp_dir()
    
    def log(self, message, level='debug', exception=None):
        """统一的日志记录方法
        
        Args:
            message: 日志消息
            level: 日志级别（debug, info, warning, error, critical）
            exception: 异常对象（可选）
        """
        log_methods = {
            'debug': logger.debug,
            'info': logger.info,
            'warning': logger.warning,
            'error': logger.error,
            'critical': logger.critical
        }
        
        # 确保level是有效的
        if level not in log_methods:
            level = 'debug'
        
        # 记录消息
        log_method = log_methods[level]
        if exception:
            log_method(f"{message}: {str(exception)}")
            if level in ['error', 'critical']:
                logger.debug(traceback.format_exc())
        else:
            log_method(message)
    
    def silent_log(self, message):
        """记录内部操作的日志，修改为DEBUG级别降低可见性"""
        self.log(message, 'debug')
    
    def get_device_id(self):
        """获取设备唯一标识，并缓存以保证稳定性"""
        # 首先尝试从缓存文件加载设备ID
        device_id_file = os.path.join(os.path.expanduser("~"), ".amazon_device_id")
        if os.path.exists(device_id_file):
            try:
                with open(device_id_file, 'r') as f:
                    device_id = f.read().strip()
                    if device_id:
                        logger.debug(f"从缓存加载设备ID: {device_id}")
                        return device_id
            except Exception as e:
                logger.debug(f"读取设备ID缓存失败: {str(e)}")
        
        try:
            # 获取主机名
            hostname = socket.gethostname()
            
            # 收集多个用于标识的值
            identifiers = []
            
            # 添加主机名
            identifiers.append(hostname)
            
            # 添加系统信息
            identifiers.append(platform.system())
            identifiers.append(platform.release())
            
            try:
                # 尝试获取MAC地址（更稳定的标识符）
                mac = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) for elements in range(0, 8*6, 8)][::-1])
                identifiers.append(mac)
            except:
                # 如果无法获取MAC地址，使用UUID
                identifiers.append(str(uuid.uuid4()))
            
            # 尝试获取CPUID或其他硬件标识符
            try:
                if platform.system() == "Windows":
                    # Windows下尝试获取处理器ID
                    if 'wmi' in sys.modules:
                        try:
                            c = wmi.WMI()
                            for processor in c.Win32_Processor():
                                identifiers.append(processor.ProcessorId.strip())
                                break
                        except Exception as e:
                            logger.debug(f"获取Windows处理器ID失败: {str(e)}")
                            identifiers.append("windows-processor-fixed-id")
                    else:
                        # 如果wmi模块不可用，使用替代方法
                        identifiers.append("windows-fixed-id-" + hostname)
                elif platform.system() == "Linux":
                    # Linux下尝试获取CPU信息
                    try:
                        with open('/proc/cpuinfo', 'r') as f:
                            for line in f:
                                if line.startswith('Serial'):
                                    identifiers.append(line.split(':')[1].strip())
                                    break
                    except:
                        pass
            except:
                # 如果获取硬件ID失败，添加一个额外的随机但固定的值
                identifiers.append("fixed-random-value-7382910")
            
            # 将所有标识符连接起来创建指纹
            fingerprint = "-".join(identifiers)
            
            # 使用SHA256创建稳定的哈希值
            hash_obj = hashlib.sha256(fingerprint.encode())
            device_id = hash_obj.hexdigest()[:16]  # 取前16个字符作为设备ID
            
            # 保存设备ID到缓存文件
            try:
                with open(device_id_file, 'w') as f:
                    f.write(device_id)
                logger.debug(f"已保存设备ID到缓存: {device_id}")
            except Exception as e:
                logger.debug(f"保存设备ID缓存失败: {str(e)}")
            
            return device_id
        except Exception:
            # 如果获取失败，返回一个默认值
            return "unknown-device"
            
    def load_license(self):
        """从本地文件加载许可证信息，支持加密存储"""
        license_file = self.license_file
        encrypted_data = self.safe_file_operation('read', license_file, mode='rb')
        
        if not encrypted_data:
            return None
        
        try:
            # 检查是否使用了加密存储
            if encrypted_data.startswith(b'gAAAAA'):  # Fernet加密标识
                try:
                    if CRYPTOGRAPHY_AVAILABLE:
                        # 获取设备特有信息作为密钥
                        device_id = self.get_device_id()
                        # 派生密钥
                        kdf = PBKDF2HMAC(
                            algorithm=hashes.SHA256(),
                            length=32,
                            salt=b'AmazonLicenseSalt',
                            iterations=100000,
                            backend=default_backend()
                        )
                        key_bytes = kdf.derive(device_id.encode())
                        fernet_key = base64.urlsafe_b64encode(key_bytes)
                        
                        # 解密
                        f = Fernet(fernet_key)
                        decrypted_data = f.decrypt(encrypted_data)
                        license_data = json.loads(decrypted_data.decode())
                        
                        # 验证完整性
                        stored_checksum = self.safe_file_operation('read', self.license_checksum_file, mode='r')
                        if stored_checksum:
                            # 计算校验和
                            current_checksum = hashlib.sha256(encrypted_data).hexdigest()
                            if current_checksum != stored_checksum:
                                logger.warning("许可证文件校验和不匹配")
                                # 仍然返回解密的数据，但记录警告
                        
                        return license_data
                    else:
                        logger.warning("加密库不可用，无法解密许可证")
                        # 尝试以兼容方式读取
                        plain_data = self.safe_file_operation('read', license_file, mode='r')
                        if plain_data:
                            return json.loads(plain_data)
                        return None
                except Exception as e:
                    logger.error(f"解密许可证失败: {str(e)}")
                    # 尝试以兼容方式读取
                    plain_data = self.safe_file_operation('read', license_file, mode='r')
                    if plain_data:
                        return json.loads(plain_data)
                    return None
            else:
                # 常规JSON格式
                return json.loads(encrypted_data.decode())
        except Exception as e:
            # 修改为更通用的错误消息，不暴露具体错误
            logger.error(f"许可证加载失败: {str(e)}")
            return None
            
    def save_license(self, key):
        """安全保存许可证信息"""
        license_data = {"key": key, "timestamp": datetime.now().isoformat()}
        try:
            if CRYPTOGRAPHY_AVAILABLE:
                # 获取设备特有信息作为密钥
                device_id = self.get_device_id()
                
                # 派生密钥
                kdf = PBKDF2HMAC(
                    algorithm=hashes.SHA256(),
                    length=32,
                    salt=b'AmazonLicenseSalt',
                    iterations=100000,
                    backend=default_backend()
                )
                key_bytes = kdf.derive(device_id.encode())
                fernet_key = base64.urlsafe_b64encode(key_bytes)
                
                # 使用Fernet加密
                f = Fernet(fernet_key)
                encrypted_data = f.encrypt(json.dumps(license_data).encode())
                
                # 保存加密后的许可证
                success = self.safe_file_operation('write', self.license_file, encrypted_data, mode='wb')
                
                if success:
                    # 计算并保存校验和
                    checksum = hashlib.sha256(encrypted_data).hexdigest()
                    self.safe_file_operation('write', self.license_checksum_file, checksum, mode='w')
                    logger.debug("许可证已安全加密保存")
                else:
                    logger.error("许可证保存失败")
            else:
                # 如果加密库不可用，则使用普通JSON存储
                success = self.safe_file_operation('write', self.license_file, json.dumps(license_data), mode='w')
                if success:
                    logger.debug("许可证已保存（未加密）")
                else:
                    logger.error("许可证保存失败")
        except Exception as e:
            # 简化错误消息
            logger.error(f"许可证保存失败: {str(e)}")
    
    # 添加XOR解密函数
    def xor_decrypt(self, data, key):
        """使用XOR对数据进行解密"""
        key_bytes = key.encode() if isinstance(key, str) else key
        data_bytes = data if isinstance(data, bytes) else data.encode()
        
        # 循环使用密钥的每个字节与数据进行异或操作
        key_len = len(key_bytes)
        decrypted = bytearray(len(data_bytes))
        
        for i in range(len(data_bytes)):
            decrypted[i] = data_bytes[i] ^ key_bytes[i % key_len]
        
        return bytes(decrypted)
    
    def decrypt_content(self, encrypted_content):
        """解密内容"""
        try:
            logger.info(f"开始解密内容，大小: {len(encrypted_content)} 字节")
            
            # 使用固定密钥
            key = "AmazonLicenseSecretKey2023"
            
            # 检查内容格式
            content_start = encrypted_content[:20]
            logger.info(f"加密内容前20字节(Base64): {base64.b64encode(content_start[:20]).decode() if isinstance(content_start, bytes) else 'Not bytes'}")
            
            # Base64解码
            try:
                decoded_content = base64.b64decode(encrypted_content)
                logger.info(f"Base64解码成功，解码后大小: {len(decoded_content)} 字节")
            except Exception as e:
                logger.error(f"Base64解码失败: {str(e)}")
                return None
            
            # 检查加密方法标识头
            if len(decoded_content) > 8:
                header = decoded_content[:8]
                data = decoded_content[8:]
                
                if header == b'AESENC01':
                    # 使用AES解密
                    logger.info("检测到AES加密内容，尝试AES解密")
                    try:
                        if CRYPTOGRAPHY_AVAILABLE:
                            # 解析组合的数据
                            salt = data[:16]
                            iv = data[16:28]
                            tag = data[28:44]
                            ciphertext = data[44:]
                            
                            # 派生密钥
                            kdf = PBKDF2HMAC(
                                algorithm=hashes.SHA256(),
                                length=32,
                                salt=salt,
                                iterations=100000,
                                backend=default_backend()
                            )
                            derived_key = kdf.derive(key.encode() if isinstance(key, str) else key)
                            
                            # 解密
                            cipher = Cipher(
                                algorithms.AES(derived_key),
                                modes.GCM(iv, tag),
                                backend=default_backend()
                            )
                            decryptor = cipher.decryptor()
                            
                            # 添加认证数据
                            decryptor.authenticate_additional_data(b"AmazonLicense")
                            
                            # 解密数据
                            decrypted_content = decryptor.update(ciphertext) + decryptor.finalize()
                            logger.info(f"AES解密成功，解密后大小: {len(decrypted_content)} 字节")
                        else:
                            logger.warning("检测到AES加密内容，但cryptography库不可用，回退到XOR解密")
                            decrypted_content = self.xor_decrypt(data, key)
                            logger.info(f"XOR解密成功，解密后大小: {len(decrypted_content)} 字节")
                    except Exception as e:
                        logger.error(f"AES解密失败: {str(e)}，尝试回退到XOR解密")
                        try:
                            # 如果AES解密失败，尝试XOR解密
                            decrypted_content = self.xor_decrypt(data, key)
                            logger.info(f"回退到XOR解密成功，解密后大小: {len(decrypted_content)} 字节")
                        except Exception as e2:
                            logger.error(f"回退解密也失败: {str(e2)}")
                            return None
                elif header == b'XORENC01':
                    # 使用XOR解密
                    logger.info("检测到XOR加密内容")
                    try:
                        decrypted_content = self.xor_decrypt(data, key)
                        logger.info(f"XOR解密成功，解密后大小: {len(decrypted_content)} 字节")
                    except Exception as e:
                        logger.error(f"XOR解密失败: {str(e)}")
                        return None
                else:
                    # 未识别的头部，尝试兼容模式（直接XOR解密整个内容）
                    logger.info(f"未识别的加密头部: {header}，尝试兼容模式")
                    try:
                        decrypted_content = self.xor_decrypt(decoded_content, key)
                        logger.info(f"兼容模式XOR解密成功，解密后大小: {len(decrypted_content)} 字节")
                    except Exception as e:
                        logger.error(f"兼容模式解密失败: {str(e)}")
                        return None
            else:
                # 内容太短，无法包含有效的头部，尝试兼容模式
                logger.info("内容太短，无法包含有效的头部，尝试兼容模式解密")
                try:
                    decrypted_content = self.xor_decrypt(decoded_content, key)
                    logger.info(f"兼容模式XOR解密成功，解密后大小: {len(decrypted_content)} 字节")
                except Exception as e:
                    logger.error(f"兼容模式解密失败: {str(e)}")
                    return None
            
            # 验证解密内容是否为有效Python代码
            content_start = decrypted_content[:100].decode('utf-8', errors='ignore')
            logger.info(f"解密内容的前100个字符: {content_start}")
            
            # 检查是否包含授权客户端特有的代码片段
            if "亚马逊授权客户端" in content_start:
                logger.error("警告：解密后的内容似乎是授权客户端代码，而不是期望的采集工具")
            
            # 计算文件的哈希值，便于比较
            content_hash = hashlib.md5(decrypted_content).hexdigest()
            logger.info(f"解密内容MD5哈希值: {content_hash}")
            
            return decrypted_content
                
        except Exception as e:
            logger.error(f"解密内容失败: {str(e)}")
            return None
    
    def check_and_download_icon(self):
        """检查并下载图标文件到固定的应用数据目录"""
        # 如果内存中已有图标数据，则不需要下载
        if self.icon_data is not None:
            return
            
        # 尝试按优先级加载图标：用户目录 > 应用数据目录 > 基础目录
        icon_locations = [
            (os.path.join(os.path.expanduser("~"), "icon.ico"), "用户目录"),
            (os.path.join(self.data_dir, "icon.ico"), "应用数据目录"),
            (os.path.join(self.base_dir, "icon.ico"), "基础目录")
        ]
        
        # 打包环境下优先检查可执行文件目录
        if getattr(sys, 'frozen', False):
            exe_dir = os.path.dirname(sys.executable)
            exe_icon = os.path.join(exe_dir, "icon.ico")
            if os.path.exists(exe_icon):
                icon_locations.insert(0, (exe_icon, "可执行文件目录"))
                
            # 检查PyInstaller临时目录
            try:
                import tempfile
                temp_dir = tempfile._get_default_tempdir()
                # 尝试多种可能的PyInstaller临时资源位置
                for mei_dir in ["_MEI", "_MEIxxxx"]:
                    temp_icon = os.path.join(temp_dir, mei_dir, "icon.ico")
                    if os.path.exists(temp_icon):
                        icon_locations.insert(0, (temp_icon, "PyInstaller临时目录"))
                        break
            except:
                pass
        
        for icon_path, location_name in icon_locations:
            if os.path.exists(icon_path):
                try:
                    with open(icon_path, 'rb') as f:
                        self.icon_data = f.read()
                    logger.debug(f"从{location_name}加载图标: {icon_path}")
                    
                    # 如果从用户目录或基础目录加载，复制到应用数据目录
                    if location_name != "应用数据目录":
                        try:
                            data_icon_path = os.path.join(self.data_dir, "icon.ico")
                            with open(data_icon_path, 'wb') as f:
                                f.write(self.icon_data)
                            logger.debug(f"已将图标复制到应用数据目录: {data_icon_path}")
                        except Exception as e:
                            logger.debug(f"复制图标到应用数据目录失败: {str(e)}")
                    return
                except Exception as e:
                    logger.debug(f"从{location_name}读取图标失败: {str(e)}")
            
        # 如果没有图标文件，尝试下载
        license_data = self.load_license()
        if license_data and license_data.get("key"):
            self.download_icon(license_data.get("key"))
            
    def download_icon(self, key):
        """从服务器下载图标文件到固定的应用数据目录"""
        try:
            logger.debug("正在下载图标文件")
            
            # 构建下载URL
            url = f"{self.server_url}/license/download_icon"
            params = {
                "key": key,
                "device_id": self.get_device_id()
            }
            
            # 发送下载请求
            response = requests.get(url, params=params)
            
            # 检查响应
            if response.status_code != 200:
                logger.debug("图标下载失败")
                return False
            
            # 保存图标数据到内存
            self.icon_data = response.content
            
            # 保存到应用数据目录
            icon_path = os.path.join(self.data_dir, "icon.ico")
            with open(icon_path, 'wb') as f:
                f.write(response.content)
            
            logger.debug(f"图标已下载到应用数据目录: {icon_path}")
            return True
            
        except Exception as e:
            logger.debug(f"图标下载过程出错: {str(e)}")
            return False
    
    def download_extension(self, key):
        """下载欧陆扩展到固定的应用数据目录并解压"""
        try:
            # 简化消息
            logger.debug("处理欧陆扩展...")
            
            # 检查欧陆扩展是否已存在
            extension_dir = os.path.join(self.data_dir, "oalur-extension-V1.9.3_3")
            if os.path.exists(extension_dir):
                logger.debug(f"欧陆扩展已存在于应用数据目录: {extension_dir}")
                return True, "扩展已存在"
            
            # 构建下载URL
            url = f"{self.server_url}/extension/download"
            params = {
                "key": key,
                "device_id": self.get_device_id()
            }
            
            # 发送下载请求
            response = requests.get(url, params=params)
            
            # 检查响应
            if response.status_code != 200:
                # 简化错误消息
                return False, "扩展下载失败"
            
            # 保存扩展数据到内存
            self.extension_data = response.content
            logger.debug("欧陆扩展数据已下载")
            
            # 保存扩展到应用数据目录
            extension_zip = os.path.join(self.data_dir, "oalur-extension-V1.9.3_3.zip")
            with open(extension_zip, 'wb') as f:
                f.write(response.content)
            logger.debug(f"已保存欧陆扩展文件: {extension_zip}")
            
            try:
                # 修复嵌套目录问题：检查ZIP文件结构后再决定解压路径
                with zipfile.ZipFile(io.BytesIO(self.extension_data), 'r') as zip_ref:
                    # 获取ZIP文件中的所有文件列表
                    zip_files = zip_ref.namelist()
                    
                    # 检查是否存在根目录前缀
                    has_root_dir = False
                    root_dir_prefix = "oalur-extension-V1.9.3_3/"
                    
                    for file_path in zip_files:
                        if file_path.startswith(root_dir_prefix):
                            has_root_dir = True
                            break
                    
                    # 决定解压路径
                    if has_root_dir:
                        # ZIP内已有根目录，解压到父目录避免嵌套
                        extract_dir = self.data_dir
                        logger.debug("检测到ZIP包含根目录，将解压到父目录避免嵌套")
                    else:
                        # ZIP内没有根目录，解压到指定目录
                        # 确保目录存在
                        if not os.path.exists(extension_dir):
                            os.makedirs(extension_dir)
                        extract_dir = extension_dir
                        logger.debug("ZIP不包含根目录，将解压到指定目录")
                    
                    # 执行解压操作
                    zip_ref.extractall(extract_dir)
                    
                logger.debug(f"欧陆扩展已解压到: {extension_dir if not has_root_dir else os.path.join(self.data_dir, 'oalur-extension-V1.9.3_3')}")
                return True, "扩展处理成功"
            except Exception as e:
                logger.error(f"欧陆扩展解压失败: {str(e)}")
                return False, "扩展解压失败"
                
        except Exception as e:
            # 简化错误消息
            logger.debug(f"欧陆扩展处理过程出错: {str(e)}")
            return False, "扩展处理过程出错"
            
    def download_program(self, program_type, key):
        """下载指定类型的程序到内存中"""
        try:
            logger.info(f"开始下载程序: {program_type}")
            
            # 检查程序类型是否有效
            if program_type not in self.available_programs:
                logger.error(f"无效的程序类型: {program_type}")
                return False, f"无效的程序类型: {program_type}"
            
            # 获取程序文件名
            program_file = self.available_programs[program_type]
            logger.info(f"程序文件名: {program_file}")
            
            # 构建下载URL
            url = f"{self.server_url}/license/download"
            params = {
                "key": key,
                "device_id": self.get_device_id(),
                "program": program_type
            }
            
            logger.info(f"下载URL: {url}，参数: program={program_type}")
            
            # 发送下载请求
            try:
                response = requests.get(url, params=params)
                logger.info(f"下载响应状态码: {response.status_code}")
            except Exception as e:
                logger.error(f"发送下载请求失败: {str(e)}")
                return False, "程序下载失败，请检查网络连接"
            
            # 检查响应
            if response.status_code != 200:
                logger.error(f"下载失败，状态码: {response.status_code}")
                return False, "程序下载失败"
            
            # 记录下载内容大小
            content_size = len(response.content)
            logger.info(f"下载完成，内容大小: {content_size} 字节")
            
            # 检查内容格式
            try:
                content_start = response.content[:20]
                content_start_str = str(content_start)
                logger.info(f"下载内容前20字节: {content_start_str}")
            except Exception as e:
                logger.error(f"检查内容格式失败: {str(e)}")
            
            # 创建临时目录
            if not self.create_temp_dir():
                logger.error("创建临时目录失败")
                return False, "创建临时目录失败"
            
            # 保存程序代码到内存
            self.program_code[program_type] = response.content
            
            # 解密程序代码
            logger.info("开始解密程序代码")
            decrypted_code = self.decrypt_content(response.content)
            if decrypted_code is None:
                logger.error("程序解密失败")
                return False, "程序解密失败"
                
            # 保存到临时目录（不带.encrypted后缀）
            program_name = program_file.replace(".encrypted", "")
            temp_program_path = os.path.join(self.temp_dir, program_name)
            
            logger.info(f"将解密后的程序保存到: {temp_program_path}")
            with open(temp_program_path, 'wb') as f:
                f.write(decrypted_code)
            
            # 验证文件是否正确保存
            if os.path.exists(temp_program_path):
                file_size = os.path.getsize(temp_program_path)
                logger.info(f"已保存解密后的程序，文件大小: {file_size} 字节")
            else:
                logger.error("解密后的程序文件未创建")
                return False, "保存解密程序失败"
            
            # 检查文件内容的前几行以确认是否为正确的程序
            try:
                with open(temp_program_path, 'r', encoding='utf-8', errors='ignore') as f:
                    first_lines = ''.join([f.readline() for _ in range(5)])
                logger.info(f"解密文件的前几行:\n{first_lines}")
            except Exception as e:
                logger.error(f"读取解密文件内容失败: {str(e)}")
            
            return True, temp_program_path
            
        except Exception as e:
            logger.error(f"程序下载过程出错: {str(e)}")
            return False, "程序下载过程出错"
            
    def ensure_correct_icon(self, content, program_type):
        """确保程序代码中使用的图标路径正确"""
        try:
            # 简化处理，不做实际修改
            return content
        except Exception:
            # 返回原内容
            return content
            
    def create_temp_dir(self):
        """创建临时目录用于存储程序文件"""
        try:
            if self.temp_dir is None or not os.path.exists(self.temp_dir):
                self.temp_dir = tempfile.mkdtemp(prefix="amazon_price_")
            return True
        except Exception:
            logger.error("创建临时目录失败")
            return False
            
    def cleanup_temp_dir(self):
        """安全清理临时目录和敏感数据"""
        try:
            # 首先终止所有浏览器进程
            self.kill_browser_processes()
            
            # 清理内存中的敏感数据
            if hasattr(self, 'program_code') and self.program_code:
                for program_type in self.program_code:
                    if self.program_code[program_type]:
                        try:
                            # 用零覆盖内存数据
                            self.program_code[program_type] = b'\x00' * len(self.program_code[program_type])
                        except:
                            pass
                # 清空字典
                self.program_code.clear()
            
            # 清理临时目录
            if self.temp_dir and os.path.exists(self.temp_dir):
                try:
                    # 遍历临时目录中的文件
                    for root, dirs, files in os.walk(self.temp_dir):
                        for file in files:
                            file_path = os.path.join(root, file)
                            try:
                                # 只处理Python文件和敏感数据文件
                                if file.endswith('.py') or file.endswith('.pyc') or file.endswith('.pyd'):
                                    # 获取文件大小
                                    file_size = os.path.getsize(file_path)
                                    # 用随机数据覆盖文件内容
                                    with open(file_path, 'wb') as f:
                                        f.write(os.urandom(file_size))
                            except Exception:
                                pass  # 忽略单个文件处理错误
                except Exception:
                    pass  # 忽略清理错误
                
                # 删除整个临时目录
                import shutil
                shutil.rmtree(self.temp_dir, ignore_errors=True)
                self.temp_dir = None
        except Exception as e:
            logger.debug(f"清理临时目录时出错: {str(e)}")
            # 即使出错也继续执行，不抛出异常
            
    def ensure_icon_in_temp_dir(self):
        """确保将图标复制到临时目录，供子程序使用"""
        try:
            # 确保临时目录存在
            if not self.temp_dir or not os.path.exists(self.temp_dir):
                if not self.create_temp_dir():
                    logger.debug("创建临时目录失败")
                    return False
            
            # 确保图标数据存在
            if not self.icon_data:
                self.check_and_download_icon()
                if not self.icon_data:
                    # 如果仍然没有图标数据，尝试直接从用户目录读取
                    user_icon_path = os.path.join(os.path.expanduser("~"), "icon.ico")
                    if os.path.exists(user_icon_path):
                        try:
                            with open(user_icon_path, 'rb') as f:
                                self.icon_data = f.read()
                            logger.debug(f"直接从用户目录加载图标: {user_icon_path}")
                        except Exception as e:
                            logger.debug(f"直接读取用户目录图标失败: {str(e)}")
                    
                    if not self.icon_data:
                        logger.debug("无法获取图标数据")
                        return False
            
            # 复制图标到临时目录
            temp_icon_path = os.path.join(self.temp_dir, "icon.ico")
            with open(temp_icon_path, 'wb') as f:
                f.write(self.icon_data)
            
            logger.debug(f"已将图标复制到临时目录: {temp_icon_path}")
            return True
        except Exception as e:
            logger.debug(f"复制图标到临时目录失败: {str(e)}")
            return False
    
    def ensure_extension_in_temp_dir(self):
        """确保将欧陆扩展复制到临时目录，供筛品程序使用"""
        try:
            # 确保临时目录存在
            if not self.temp_dir or not os.path.exists(self.temp_dir):
                if not self.create_temp_dir():
                    logger.debug("创建临时目录失败")
                    return False
            
            # 获取应用数据目录中的欧陆扩展路径
            extension_dir = os.path.join(self.data_dir, "oalur-extension-V1.9.3_3")
            
            # 检查应用数据目录中是否有欧陆扩展
            if not os.path.exists(extension_dir):
                logger.debug("应用数据目录中未找到欧陆扩展")
                # 尝试从工作目录复制
                base_extension_dir = os.path.join(self.base_dir, "oalur-extension-V1.9.3_3")
                if os.path.exists(base_extension_dir):
                    extension_dir = base_extension_dir
                else:
                    logger.debug("未找到欧陆扩展目录")
                    return False
            
            # 复制欧陆扩展到临时目录
            temp_extension_dir = os.path.join(self.temp_dir, "oalur-extension-V1.9.3_3")
            
            # 如果目标路径已存在，先删除
            if os.path.exists(temp_extension_dir):
                import shutil
                shutil.rmtree(temp_extension_dir)
            
            # 执行复制
            import shutil
            shutil.copytree(extension_dir, temp_extension_dir)
            
            # 创建唯一的浏览器用户数据目录
            browser_data_dir = os.path.join(self.temp_dir, f"browser_data_{int(time.time())}_{secrets.token_hex(4)}")
            if not os.path.exists(browser_data_dir):
                os.makedirs(browser_data_dir)
            logger.debug(f"已创建浏览器唯一数据目录: {browser_data_dir}")
            
            # 创建标记文件，告知程序使用这个用户数据目录
            browser_config_file = os.path.join(self.temp_dir, "browser_config.json")
            with open(browser_config_file, 'w', encoding='utf-8') as f:
                json.dump({"user_data_dir": browser_data_dir}, f)
            
            logger.debug(f"已将欧陆扩展复制到临时目录: {temp_extension_dir}")
            return True
        except Exception as e:
            logger.debug(f"复制欧陆扩展到临时目录失败: {str(e)}")
            return False
            
    def prepare_program_environment(self, program_type):
        """准备程序运行环境，下载并解密代码"""
        logger.info(f"准备程序环境: {program_type}")
        
        # 加载许可证
        license_data = self.load_license()
        if not license_data or not license_data.get("key"):
            logger.error("未找到有效的许可证")
            return False, "未找到有效的许可证"
        
        key = license_data.get("key")
        logger.info(f"已加载许可证密钥: {key[:4]}...{key[-4:] if len(key) > 8 else ''}")
        
        # 检查许可证
        valid, message, data = self.check_license(key)
        if not valid:
            logger.error(f"许可证检查失败: {message}")
            return False, message
        
        logger.info("许可证检查通过")
        
        # 验证程序权限 - 从正确的嵌套结构中获取allowed_programs
        license_info = data.get("license_info", {})
        allowed_programs = license_info.get("allowed_programs", [])
        logger.info(f"允许的程序: {allowed_programs}")
        
        if program_type not in allowed_programs:
            logger.error(f"无权限运行程序: {program_type}")
            return False, f"当前许可证不包含 {program_type} 程序的权限"
        
        # 下载图标（如果还没有）
        if not self.icon_data:
            self.check_and_download_icon()
        
        # 确保临时目录已创建
        if not self.create_temp_dir():
            logger.error("创建临时目录失败")
            return False, "创建临时目录失败"
            
        # 确保图标被复制到临时目录，供子程序使用
        if not self.ensure_icon_in_temp_dir():
            logger.warning("复制图标到临时目录失败，子程序可能无法正常显示图标")
            # 继续执行，因为没有图标不影响主要功能
        
        # 如果是筛品程序，需要下载欧陆扩展并复制到临时目录
        if program_type == "筛品":
            # 下载欧陆扩展（如果还没有）
            if not self.extension_data:
                ext_success, ext_message = self.download_extension(key)
                if not ext_success:
                    logger.warning(f"扩展下载失败: {ext_message}")
                    # 不中断程序运行，因为扩展可能不是必需的
            
            # 复制欧陆扩展到临时目录
            if not self.ensure_extension_in_temp_dir():
                logger.warning("复制欧陆扩展到临时目录失败，筛品程序可能无法正常使用")
                # 继续执行，保持兼容性
        
        # 下载程序
        logger.info(f"开始下载程序: {program_type}")
        program_success, program_result = self.download_program(program_type, key)
        if not program_success:
            logger.error(f"程序下载失败: {program_result}")
            return False, program_result
        
        logger.info(f"程序下载成功，路径: {program_result}")
        
        # 复制UI主题文件到临时目录（如果存在）
        ui_theme_path = os.path.join(self.base_dir, "ui_theme.py")
        if os.path.exists(ui_theme_path):
            try:
                import shutil
                dest_path = os.path.join(self.temp_dir, "ui_theme.py")
                shutil.copy2(ui_theme_path, dest_path)
                logger.debug(f"UI主题文件已复制: {dest_path}")
            except Exception as e:
                logger.warning(f"复制UI主题文件失败: {str(e)}")
        
        # 返回程序文件路径
        return True, program_result
            
    def check_license(self, key):
        """安全检查许可证是否有效"""
        try:
            # 生成请求签名和时间戳
            timestamp = str(int(time.time()))
            device_id = self.get_device_id()
            nonce = secrets.token_hex(8)  # 添加随机数防止重放攻击
            
            # 构建签名数据
            signature_data = f"{key}:{device_id}:{timestamp}:{nonce}"
            signature = hashlib.sha256(signature_data.encode()).hexdigest()
            
            # 构建请求
            url = f"{self.server_url}/license/check"
            data = {
                "key": key,
                "device_id": device_id,
                "timestamp": timestamp,
                "nonce": nonce,
                "signature": signature,
                "client_version": "2.1.1"  # 添加客户端版本信息
            }
            
            # 发送请求，添加重试和超时
            max_retries = 3
            retry_count = 0
            
            while retry_count < max_retries:
                try:
                    response = requests.post(
                        url, 
                        json=data, 
                        timeout=10  # 设置10秒超时
                    )
                    break
                except requests.exceptions.RequestException as e:
                    retry_count += 1
                    if retry_count >= max_retries:
                        logger.error(f"请求失败，已达最大重试次数: {str(e)}")
                        return False, "许可证验证失败，请检查网络连接", None
                    logger.warning(f"请求失败，正在重试 ({retry_count}/{max_retries}): {str(e)}")
                    time.sleep(1)  # 重试前等待1秒
            
            # 检查响应
            if response.status_code != 200:
                return False, f"服务器响应错误 (状态码: {response.status_code})", None
            
            result = response.json()
            
            # 验证服务器响应完整性（如果服务器返回了签名）
            if 'server_signature' in result:
                server_data = f"{result.get('valid')}:{key}:{timestamp}:{nonce}"
                expected_signature = hashlib.sha256(server_data.encode()).hexdigest()
                
                if result.get('server_signature') != expected_signature:
                    logger.warning("服务器响应签名无效")
                    # 继续处理，但记录警告
            
            # 检查结果
            if result.get("valid"):
                return True, "许可证有效", result
            else:
                return False, result.get("message", "许可证无效"), None
                
        except Exception as e:
            # 记录详细错误，但向用户提供简化消息
            logger.error(f"许可证检查失败: {str(e)}")
            return False, "许可证验证失败，请检查网络连接", None
            
    def get_python_interpreter(self):
        """获取正确的Python解释器路径"""
        try:
            # 检查是否是打包后的环境
            if getattr(sys, 'frozen', False):
                # 先尝试使用当前可执行文件所在目录的Python
                exe_dir = os.path.dirname(sys.executable)
                python_in_exe_dir = os.path.join(exe_dir, 'python.exe' if platform.system() == "Windows" else 'python3')
                if os.path.exists(python_in_exe_dir):
                    logger.info(f"使用程序目录下的Python解释器: {python_in_exe_dir}")
                    return python_in_exe_dir
                
                # 定义查找方法
                python_exe = None
                
                # 从PATH环境变量查找
                commands = {
                    "Windows": ["where", ["python.exe", "python3.exe"]],
                    "Darwin": ["which", ["python", "python3"]],  # macOS
                    "Linux": ["which", ["python", "python3"]]
                }
                
                system = platform.system()
                if system in commands:
                    cmd, executables = commands[system]
                    logger.info(f"尝试从PATH中查找Python解释器")
                    
                    for exe in executables:
                        try:
                            result = subprocess.run(
                                [cmd, exe], 
                                capture_output=True, 
                                text=True,
                                check=False
                            )
                            if result.returncode == 0 and result.stdout.strip():
                                python_exe = result.stdout.strip().split('\n')[0]
                                logger.info(f"从PATH找到Python解释器: {python_exe}")
                                
                                # 验证解释器是否可用
                                try:
                                    version = subprocess.check_output([python_exe, "-V"], stderr=subprocess.STDOUT).decode()
                                    if 'Python' in version:
                                        logger.info(f"验证Python解释器成功: {python_exe}, 版本: {version.strip()}")
                                        return python_exe
                                except:
                                    logger.debug(f"解释器{python_exe}无法验证，继续查找")
                                    continue
                        except Exception as e:
                            logger.debug(f"查找{exe}时出错: {str(e)}")
                
                # 如果在Windows上，尝试使用py启动器
                if system == "Windows" and not python_exe:
                    try:
                        result = subprocess.run(["py", "-3", "-V"], capture_output=True, text=True, check=False)
                        if result.returncode == 0 and "Python" in result.stdout:
                            logger.info("找到py启动器")
                            return "py"
                    except:
                        pass
                
                # 如果仍然没有找到解释器，返回None
                logger.warning("无法找到Python解释器，将使用内置执行模式")
                return None
            else:
                # 开发环境，直接使用sys.executable
                logger.info(f"使用当前Python解释器: {sys.executable}")
                return sys.executable
        except Exception as e:
            logger.error(f"获取Python解释器路径时出错: {str(e)}")
            return None
            
    def run_program(self, program_type):
        """运行指定类型的程序"""
        logger.info(f"开始运行程序: {program_type}")
        
        # 检查是否已经在运行
        if program_type in self.running_threads and self.running_threads[program_type].is_alive():
            logger.info(f"{program_type}程序已在运行")
            messagebox.showinfo("提示", f"{program_type}程序已在运行")
            return
        
        # 检查并安装所需依赖 - 使用缓存机制，此处不会频繁执行完整检查
        self.check_and_install_dependencies()
        
        # 准备程序环境
        logger.info("准备程序环境...")
        success, result = self.prepare_program_environment(program_type)
        if not success:
            logger.error(f"准备程序环境失败: {result}")
            messagebox.showerror("错误", result)
            return
        
        # 获取程序文件路径
        program_path = result
        logger.info(f"程序路径: {program_path}")
        
        # 定义线程函数
        def run_program_in_thread():
            try:
                logger.info(f"启动程序线程: {program_type}")
                
                # 获取程序类型对应的输出目录
                output_dir = self.get_program_output_dir(program_type)
                
                # 第1步：优先尝试在内存中执行程序（不创建新进程）
                logger.info("尝试在内存中直接执行程序")
                memory_success = self.run_program_in_memory(program_path, output_dir)
                
                if memory_success:
                    logger.info(f"程序在内存中成功执行: {program_type}")
                    return
                
                # 如果内存执行失败，则尝试使用Python解释器
                logger.info("内存执行失败，尝试使用Python解释器")
                
                # 获取Python解释器路径
                python_exe = self.get_python_interpreter()
                
                if python_exe:
                    logger.info(f"使用Python解释器: {python_exe}")
                    
                    # 在Windows平台上使用启动信息隐藏控制台窗口
                    if platform.system() == "Windows":
                        # 配置启动信息
                        startupinfo = subprocess.STARTUPINFO()
                        startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                        startupinfo.wShowWindow = 0  # SW_HIDE
                        
                        # 组装命令, 添加--output-dir参数指向程序类型对应的子目录
                        cmd = [python_exe, program_path, "--output-dir", output_dir]
                        logger.info(f"执行命令: {' '.join(cmd)}")
                        
                        # 执行命令 - 修改工作目录为输出目录而不是程序目录
                        process = subprocess.Popen(
                            cmd,
                            cwd=output_dir,  # 将工作目录设置为输出目录而不是程序目录
                            startupinfo=startupinfo,
                            creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
                        )
                        
                        # 记录进程信息
                        logger.info(f"进程ID: {process.pid}")
                        
                        # 为"筛品"程序添加浏览器进程监控
                        if program_type == "筛品":
                            # 初始化此程序类型的浏览器进程列表
                            if program_type not in self.browser_processes:
                                self.browser_processes[program_type] = []
                                
                            # 启动浏览器进程监控线程
                            def monitor_browser_processes():
                                try:
                                    logger.info("启动浏览器进程监控")
                                    import psutil
                                    
                                    # 初始监控延迟，等待浏览器启动
                                    time.sleep(5)
                                    
                                    # 监控循环，每5秒检查一次
                                    while psutil.pid_exists(process.pid):
                                        try:
                                            # 查找Chrome、Firefox进程，检查它们的命令行是否包含temp_dir
                                            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                                                try:
                                                    if proc.info['name'].lower() in ['chrome.exe', 'firefox.exe', 'chromedriver.exe', 'geckodriver.exe']:
                                                        # 检查命令行参数是否包含temp_dir
                                                        cmdline = proc.info['cmdline']
                                                        pid = proc.info['pid']
                                                        
                                                        if cmdline and self.temp_dir and any(self.temp_dir in arg for arg in cmdline if arg):
                                                            if pid not in self.browser_processes[program_type]:
                                                                self.browser_processes[program_type].append(pid)
                                                                logger.info(f"发现并跟踪浏览器进程: {pid}")
                                                except Exception:
                                                    continue
                                            
                                            # 验证已跟踪的浏览器进程是否仍然存在
                                            for pid in self.browser_processes[program_type][:]:
                                                if not psutil.pid_exists(pid):
                                                    self.browser_processes[program_type].remove(pid)
                                                    logger.info(f"浏览器进程 {pid} 已结束")
                                            
                                            # 等待下一次检查
                                            time.sleep(5)
                                        except Exception as e:
                                            logger.debug(f"浏览器进程监控迭代出错: {str(e)}")
                                    
                                    logger.info("筛品程序已结束，浏览器进程监控停止")
                                    
                                    # 程序结束后，终止所有相关的浏览器进程
                                    try:
                                        for pid in self.browser_processes[program_type][:]:
                                            try:
                                                if psutil.pid_exists(pid):
                                                    proc = psutil.Process(pid)
                                                    logger.info(f"程序结束后终止浏览器进程: {pid}")
                                                    proc.terminate()
                                                self.browser_processes[program_type].remove(pid)
                                            except Exception:
                                                pass
                                    except Exception as e:
                                        logger.debug(f"清理浏览器进程出错: {str(e)}")
                                        
                                except ImportError:
                                    logger.warning("psutil模块不可用，无法监控浏览器进程")
                                except Exception as e:
                                    logger.error(f"浏览器进程监控线程出错: {str(e)}")
                                    logger.debug(traceback.format_exc())
                            
                            # 启动监控线程
                            monitor_thread = threading.Thread(target=monitor_browser_processes)
                            monitor_thread.daemon = True
                            monitor_thread.start()
                            logger.info("浏览器进程监控线程已启动")
                        
                        # 等待程序完成
                        return_code = process.wait()
                        logger.info(f"程序已退出: {program_type}, 返回码: {return_code}")
                    else:
                        # 非Windows平台的处理
                        cmd = [python_exe, program_path, "--output-dir", output_dir]
                        logger.info(f"执行命令: {' '.join(cmd)}")
                        
                        process = subprocess.Popen(
                            cmd,
                            cwd=output_dir,  # 将工作目录设置为输出目录而不是程序目录
                            stdout=subprocess.DEVNULL,
                            stderr=subprocess.DEVNULL
                        )
                        
                        # 等待程序完成
                        return_code = process.wait()
                        logger.info(f"程序已退出: {program_type}, 返回码: {return_code}")
                else:
                    # 如果找不到Python解释器，显示错误
                    error_msg = "无法找到Python解释器，请确保系统已正确安装Python"
                    logger.error(error_msg)
                    messagebox.showerror("错误", error_msg)
                
            except Exception as e:
                # 记录详细错误消息
                logger.error(f"运行程序时出错: {program_type}, 错误: {str(e)}")
                # 显示错误消息以及详细的堆栈跟踪
                error_msg = f"运行程序时出错: {str(e)}"
                logger.error(traceback.format_exc())  # 记录详细堆栈
                messagebox.showerror("程序运行错误", error_msg)
        
        # 创建并启动线程
        logger.info(f"创建程序运行线程: {program_type}")
        thread = threading.Thread(target=run_program_in_thread)
        thread.daemon = True  # 设置为守护线程
        thread.start()
        
        # 保存线程引用
        self.running_threads[program_type] = thread
        logger.info(f"程序线程已启动: {program_type}")
    
    def run_program_in_memory(self, program_path, output_dir=None):
        """在内存中直接执行解密后的Python程序代码"""
        try:
            logger.info(f"尝试在内存中执行程序: {program_path}")
            
            # 如果没有指定输出目录，使用默认目录
            if output_dir is None:
                output_dir = self.base_dir
            
            # 读取程序文件
            with open(program_path, 'rb') as f:
                program_code = f.read()
            
            # 创建临时模块来执行代码
            module_name = f"amazon_program_{int(time.time())}"
            
            # 创建空模块
            spec = importlib.machinery.ModuleSpec(module_name, None)
            module = importlib.util.module_from_spec(spec)
            
            # 设置必要的属性
            module.__file__ = program_path
            module.__name__ = "__main__"  # 确保作为主模块执行
            
            # 保存原始的sys.argv、sys.path和当前工作目录
            original_argv = sys.argv.copy()
            original_path = sys.path.copy()
            original_cwd = os.getcwd()
            
            try:
                # 设置程序运行时的参数，添加--output-dir参数指向对应的输出目录
                sys.argv = [program_path, "--output-dir", output_dir]
                
                # 确保程序目录在sys.path中
                prog_dir = os.path.dirname(program_path)
                if prog_dir not in sys.path:
                    sys.path.insert(0, prog_dir)
                
                # 切换到输出目录
                os.chdir(output_dir)
                
                # 执行程序代码
                logger.info("开始在内存中执行程序代码")
                
                # 创建全局环境
                globals_dict = globals().copy()
                
                # 确保标准模块可用
                globals_dict['__name__'] = '__main__'
                globals_dict['__file__'] = program_path
                
                # 确保一些常用库已导入
                for module_name in ['os', 'sys', 'time', 'tkinter', 'json', 'requests', 'pandas', 'selenium']:
                    try:
                        globals_dict[module_name] = __import__(module_name)
                    except ImportError:
                        pass  # 忽略导入失败的模块
                
                # 执行代码
                exec(program_code, globals_dict)
                
                logger.info("程序在内存中执行完成")
                return True
            finally:
                # 恢复原始的sys.argv、sys.path和当前工作目录
                sys.argv = original_argv
                sys.path = original_path
                os.chdir(original_cwd)
                
        except Exception as e:
            logger.error(f"在内存中执行程序失败: {str(e)}")
            logger.error(traceback.format_exc())
            return False
    
    def check_environment_integrity(self):
        """检查运行环境完整性"""
        try:
            logger.debug("执行环境完整性检查")
            
            # 检测调试器
            self.check_for_debugger()
            
            # 检测虚拟机环境
            if self.is_virtual_machine() and not getattr(self, 'allow_vm', False):
                logger.warning("检测到虚拟机环境")
                # 这里只记录警告但不中断，以免影响正常用户
            
            # 检查程序文件完整性
            if getattr(sys, 'frozen', False):
                executable_path = sys.executable
                try:
                    # 计算可执行文件哈希
                    current_hash = self.calculate_file_hash(executable_path)
                    
                    # 检查是否存在已保存的哈希值
                    hash_file = os.path.join(self.data_dir, ".file_hash")
                    if os.path.exists(hash_file):
                        with open(hash_file, 'r') as f:
                            stored_hash = f.read().strip()
                            if current_hash != stored_hash:
                                logger.warning("程序文件可能被篡改")
                    else:
                        # 首次运行，保存哈希值
                        with open(hash_file, 'w') as f:
                            f.write(current_hash)
                except Exception as e:
                    logger.debug(f"文件完整性检查失败: {str(e)}")
            
            return True
        except Exception as e:
            logger.error(f"环境完整性检查失败: {str(e)}")
            return False
    
    def check_for_debugger(self):
        """检测是否在调试器下运行"""
        try:
            # 检查系统跟踪函数
            if sys.gettrace() is not None:
                logger.warning("检测到调试器")
                return True
            
            # Windows特定检测
            if platform.system() == "Windows":
                try:
                    # 尝试使用IsDebuggerPresent API
                    import ctypes
                    if ctypes.windll.kernel32.IsDebuggerPresent() != 0:
                        logger.warning("Windows API检测到调试器")
                        return True
                except Exception:
                    pass
            
            return False
        except Exception:
            return False
    
    def is_virtual_machine(self):
        """检测是否在虚拟机中运行"""
        try:
            # 检查常见虚拟机特征
            if platform.system() == "Windows":
                try:
                    if 'wmi' in sys.modules:
                        c = wmi.WMI()
                        for model in c.Win32_ComputerSystem():
                            manufacturer = model.Manufacturer.lower() if model.Manufacturer else ''
                            model_name = model.Model.lower() if model.Model else ''
                            
                            vm_indicators = ['vmware', 'virtual', 'vbox', 'virtualbox', 'kvm', 'qemu']
                            for indicator in vm_indicators:
                                if indicator in manufacturer or indicator in model_name:
                                    return True
                except Exception:
                    pass
            
            # 检查是否有虚拟机相关进程
            try:
                import psutil
                vm_processes = ['vmtoolsd.exe', 'VBoxService.exe', 'vmwaretray.exe']
                for proc in psutil.process_iter(['name']):
                    if proc.info['name'] in vm_processes:
                        return True
            except (ImportError, Exception):
                pass
                
            return False
        except Exception:
            return False
    
    def calculate_file_hash(self, file_path):
        """计算文件的SHA-256哈希值"""
        try:
            # 只读取文件的前1MB来计算哈希，避免大文件处理过慢
            chunk_size = 1024 * 1024
            hash_obj = hashlib.sha256()
            with open(file_path, 'rb') as f:
                chunk = f.read(chunk_size)
                hash_obj.update(chunk)
            return hash_obj.hexdigest()
        except Exception as e:
            logger.debug(f"计算文件哈希值失败: {str(e)}")
            return "unknown-hash"
    
    def get_program_output_dir(self, program_type):
        """根据程序类型获取输出目录"""
        # 在主程序目录下创建对应程序类型的子文件夹
        output_dir = os.path.join(self.base_dir, program_type)
        
        # 确保目录存在
        try:
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)
        except Exception as e:
            # 如果无法创建目录，使用主程序目录作为备选
            output_dir = self.base_dir
        
        return output_dir
    
    def kill_browser_processes(self):
        """终止所有跟踪的浏览器进程"""
        logger.info("开始清理浏览器进程...")
        
        if not self.browser_processes:
            logger.info("没有需要清理的浏览器进程")
            return
            
        try:
            import psutil
            killed_count = 0
            
            # 终止所有记录的浏览器进程
            for program_type, processes in self.browser_processes.items():
                for pid in processes[:]:  # 使用副本进行遍历
                    try:
                        if psutil.pid_exists(pid):
                            process = psutil.Process(pid)
                            logger.info(f"正在终止浏览器进程: {pid}")
                            process.terminate()
                            killed_count += 1
                    except Exception as e:
                        logger.debug(f"终止进程 {pid} 失败: {str(e)}")
                
                # 清空该程序类型的进程列表
                self.browser_processes[program_type] = []
            
            # 如果有进程被终止，等待一段时间确保完全关闭
            if killed_count > 0:
                logger.info(f"已终止 {killed_count} 个浏览器进程，等待进程完全关闭...")
                time.sleep(2)  # 等待2秒确保进程完全关闭
            
            # 尝试查找并终止可能未被跟踪的浏览器进程
            # 查找与临时目录相关的Chrome/Firefox进程
            if self.temp_dir:
                for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                    try:
                        cmdline = proc.info['cmdline']
                        if cmdline and self.temp_dir in ' '.join(cmdline):
                            if proc.info['name'].lower() in ['chrome.exe', 'firefox.exe', 'chromedriver.exe', 'geckodriver.exe']:
                                logger.info(f"发现未跟踪的浏览器进程: {proc.info['pid']}")
                                proc.terminate()
                                time.sleep(0.5)  # 短暂延迟
                    except Exception:
                        continue
            
            logger.info("浏览器进程清理完成")
        except ImportError:
            logger.warning("psutil模块不可用，无法清理浏览器进程")
        except Exception as e:
            logger.error(f"清理浏览器进程时出错: {str(e)}")
            logger.debug(traceback.format_exc())

class SimpleClientApp:
    def __init__(self, root, client):
        self.root = root
        
        # 创建客户端实例
        self.client = client
        
        # 确保AmazonUITheme类可以访问客户端实例（兼容性处理）
        try:
            if hasattr(AmazonUITheme, 'set_client'):
                AmazonUITheme.set_client(self.client)
            else:
                # 如果没有set_client方法，直接设置_client属性
                if hasattr(AmazonUITheme, '_client'):
                    setattr(AmazonUITheme, '_client', self.client)
        except Exception as e:
            logger.debug(f"在SimpleClientApp中设置AmazonUITheme客户端失败: {str(e)}")
        
        # 使用统一UI主题设置窗口
        AmazonUITheme.setup_window(
            self.root,
            "亚马逊蓝图工具",
            size="600x550",
            resizable=(False, False),
            icon=True
        )
        
        # 如果使用主题设置没有成功设置图标，再次尝试直接设置
        try:
            # 确保客户端已加载图标
            if not self.client.icon_data:
                self.client.check_and_download_icon()
                
            if self.client.icon_data:
                # 创建临时图标文件
                import tempfile
                icon_fd, icon_path = tempfile.mkstemp(suffix='.ico')
                os.write(icon_fd, self.client.icon_data)
                os.close(icon_fd)
                
                # 设置窗口图标
                self.root.iconbitmap(icon_path)
                logger.info(f"在SimpleClientApp中直接设置窗口图标: {icon_path}")
        except Exception as e:
            logger.debug(f"在SimpleClientApp中设置窗口图标失败: {str(e)}")
        
        # 创建界面元素
        self.create_widgets()

        # 设置窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 启动后检查更新（延迟执行避免阻塞界面）
        self.root.after(1000, self.check_for_updates)
    
    def create_widgets(self):
        """创建界面控件"""
        # 设置主窗口背景色
        self.root.configure(bg='#f0f0f0')

        # 创建主容器
        main_container = tk.Frame(self.root, bg='#f0f0f0')
        main_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 创建标题栏
        title_frame = tk.Frame(main_container, bg='#2c3e50', relief=tk.RAISED, bd=2)
        title_frame.pack(fill=tk.X, pady=(0, 20))

        # 主标题
        title_label = tk.Label(
            title_frame,
            text="🛠️ 亚马逊蓝图工具",
            font=("微软雅黑", 18, "bold"),
            fg='white',
            bg='#2c3e50',
            pady=15
        )
        title_label.pack()

        # 副标题
        subtitle_label = tk.Label(
            title_frame,
            text="Amazon Blueprint Tools - 专业版",
            font=("微软雅黑", 10),
            fg='#ecf0f1',
            bg='#2c3e50'
        )
        subtitle_label.pack(pady=(0, 10))
        
        # 创建激活码输入区域
        input_frame = tk.LabelFrame(
            main_container,
            text="🔑 激活码设置",
            font=("微软雅黑", 11, "bold"),
            fg='#2c3e50',
            bg='#f0f0f0',
            relief=tk.GROOVE,
            bd=2,
            padx=15,
            pady=10
        )
        input_frame.pack(fill=tk.X, pady=(0, 15))

        # 激活码输入行
        key_input_frame = tk.Frame(input_frame, bg='#f0f0f0')
        key_input_frame.pack(fill=tk.X, pady=5)

        key_label = tk.Label(
            key_input_frame,
            text="激活码:",
            font=("微软雅黑", 10),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        key_label.pack(side=tk.LEFT, padx=(0, 10))

        self.key_var = tk.StringVar()
        self.key_entry = tk.Entry(
            key_input_frame,
            textvariable=self.key_var,
            width=35,
            font=("Consolas", 10),
            relief=tk.SUNKEN,
            bd=2
        )
        self.key_entry.pack(side=tk.LEFT, padx=(0, 10), fill=tk.X, expand=True)

        # 创建按钮容器
        button_frame = tk.Frame(key_input_frame, bg='#f0f0f0')
        button_frame.pack(side=tk.RIGHT)

        # 创建检测更新按钮
        update_button = tk.Button(
            button_frame,
            text="🔄 检测更新",
            command=self.manual_check_updates,
            font=("微软雅黑", 9),
            bg='#e67e22',
            fg='white',
            relief=tk.RAISED,
            bd=2,
            padx=15,
            cursor='hand2'
        )
        update_button.pack(side=tk.RIGHT, padx=(0, 5))

        # 创建保存按钮
        save_button = tk.Button(
            button_frame,
            text="💾 保存",
            command=self.save_key,
            font=("微软雅黑", 9),
            bg='#3498db',
            fg='white',
            relief=tk.RAISED,
            bd=2,
            padx=15,
            cursor='hand2'
        )
        save_button.pack(side=tk.RIGHT)

        # 添加按钮悬停效果
        def on_update_enter(event):
            update_button.config(bg='#d35400')
        def on_update_leave(event):
            update_button.config(bg='#e67e22')
        def on_save_enter(event):
            save_button.config(bg='#2980b9')
        def on_save_leave(event):
            save_button.config(bg='#3498db')

        update_button.bind("<Enter>", on_update_enter)
        update_button.bind("<Leave>", on_update_leave)
        save_button.bind("<Enter>", on_save_enter)
        save_button.bind("<Leave>", on_save_leave)
        
        # 创建程序选择区域
        program_frame = tk.LabelFrame(
            main_container,
            text="🚀 工具选择",
            font=("微软雅黑", 11, "bold"),
            fg='#2c3e50',
            bg='#f0f0f0',
            relief=tk.GROOVE,
            bd=2,
            padx=15,
            pady=10
        )
        program_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        # 程序选项
        self.program_var = tk.StringVar(value="采集")

        programs = [
            ("📊 数据采集工具", "采集", "#e74c3c", "🔍 智能采集亚马逊产品数据"),
            ("🔍 产品筛选工具", "筛品", "#f39c12", "📋 高效筛选优质产品"),
            ("📈 历史价格工具", "价格", "#27ae60", "💰 追踪产品价格变化"),
            ("⚖️ 专利查询工具", "专利", "#9b59b6", "🔎 专利风险评估")
        ]

        # 创建网格布局
        for i, (text, value, color, desc) in enumerate(programs):
            # 创建每个工具的容器
            tool_frame = tk.Frame(program_frame, bg='white', relief=tk.RAISED, bd=1)
            tool_frame.grid(row=i//2, column=i%2, padx=10, pady=8, sticky='ew', ipadx=10, ipady=8)

            # 配置网格权重
            program_frame.grid_columnconfigure(0, weight=1)
            program_frame.grid_columnconfigure(1, weight=1)

            # 单选按钮
            program_button = tk.Radiobutton(
                tool_frame,
                text=text,
                value=value,
                variable=self.program_var,
                font=("微软雅黑", 10, "bold"),
                fg=color,
                bg='white',
                selectcolor='#ecf0f1',
                activebackground='white',
                cursor='hand2'
            )
            program_button.pack(anchor='w', padx=5, pady=(5, 2))

            # 描述文字
            desc_label = tk.Label(
                tool_frame,
                text=desc,
                font=("微软雅黑", 8),
                fg='#7f8c8d',
                bg='white'
            )
            desc_label.pack(anchor='w', padx=5, pady=(0, 5))
        
        # 创建运行按钮区域
        action_frame = tk.Frame(main_container, bg='#f0f0f0')
        action_frame.pack(fill=tk.X, pady=(0, 10))

        run_button = tk.Button(
            action_frame,
            text="🚀 启动选中的工具",
            command=self.run_selected_program,
            font=("微软雅黑", 12, "bold"),
            bg='#2ecc71',
            fg='white',
            relief=tk.RAISED,
            bd=3,
            padx=20,
            pady=12,
            cursor='hand2'
        )
        run_button.pack(fill=tk.X)

        # 添加按钮悬停效果
        def on_enter(event):
            run_button.config(bg='#27ae60')
        def on_leave(event):
            run_button.config(bg='#2ecc71')

        run_button.bind("<Enter>", on_enter)
        run_button.bind("<Leave>", on_leave)
        
        # 创建状态栏
        status_frame = tk.Frame(main_container, bg='#34495e', relief=tk.SUNKEN, bd=1)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM)

        # 状态信息标签
        self.status_label = tk.Label(
            status_frame,
            text="✅ 系统就绪",
            font=("微软雅黑", 9),
            fg='#ecf0f1',
            bg='#34495e',
            padx=10,
            pady=5
        )
        self.status_label.pack(side=tk.LEFT)

        # 版本信息标签
        version_label = tk.Label(
            status_frame,
            text="🔧 蓝图工具 v2.1.1",
            font=("微软雅黑", 9),
            fg='#bdc3c7',
            bg='#34495e',
            padx=10,
            pady=5
        )
        version_label.pack(side=tk.RIGHT)

        # 加载已保存的激活码
        self.load_saved_key()

    def check_for_updates(self):
        """检查程序更新（自动启动时调用）"""
        try:
            # 在后台线程中检查更新，避免阻塞UI
            def check_update_thread():
                try:
                    current_version = "2.1.1"  # 当前版本号

                    # 获取当前的授权信息
                    license_key = self.key_var.get().strip() if hasattr(self, 'key_var') else None
                    device_id = self.client.get_device_id() if hasattr(self.client, 'get_device_id') else None

                    if check_and_update(self.root, current_version, license_key, device_id):
                        # 如果用户选择更新，程序会自动退出
                        pass
                except Exception as e:
                    logger.debug(f"检查更新失败: {e}")

            # 启动检查更新线程
            update_thread = threading.Thread(target=check_update_thread)
            update_thread.daemon = True
            update_thread.start()

        except Exception as e:
            logger.debug(f"启动更新检查失败: {e}")

    def manual_check_updates(self):
        """手动检测更新"""
        try:
            # 更新状态栏显示
            self.status_label.config(text="🔄 正在检查更新...", fg='#f39c12')
            self.root.update()

            # 在后台线程中检查更新，避免阻塞UI
            def manual_update_thread():
                try:
                    current_version = "2.1.1"  # 当前版本号

                    # 获取当前的授权信息
                    license_key = self.key_var.get().strip() if hasattr(self, 'key_var') else None
                    device_id = self.client.get_device_id() if hasattr(self.client, 'get_device_id') else None

                    # 检查激活码是否存在
                    if not license_key:
                        self.root.after(0, lambda: self.status_label.config(text="⚠️ 请先输入激活码", fg='#e74c3c'))
                        self.root.after(0, lambda: messagebox.showwarning("提示", "请先输入有效的激活码再检查更新"))
                        return

                    # 调用更新检查
                    if check_and_update(self.root, current_version, license_key, device_id):
                        # 如果用户选择更新，程序会自动退出
                        pass
                    else:
                        # 更新状态栏显示检查完成
                        self.root.after(0, lambda: self.status_label.config(text="✅ 更新检查完成", fg='#27ae60'))

                except Exception as e:
                    logger.debug(f"手动检查更新失败: {e}")
                    # 更新状态栏显示错误
                    self.root.after(0, lambda: self.status_label.config(text="❌ 更新检查失败", fg='#e74c3c'))
                    self.root.after(0, lambda: messagebox.showerror("错误", f"检查更新失败: {str(e)}"))

            # 启动检查更新线程
            update_thread = threading.Thread(target=manual_update_thread)
            update_thread.daemon = True
            update_thread.start()

        except Exception as e:
            logger.debug(f"启动手动更新检查失败: {e}")
            self.status_label.config(text="❌ 更新检查失败", fg='#e74c3c')
            messagebox.showerror("错误", f"启动更新检查失败: {str(e)}")

    def load_saved_key(self):
        """加载已保存的激活码"""
        license_data = self.client.load_license()
        if license_data and "key" in license_data:
            self.key_var.set(license_data["key"])
    
    def save_key(self):
        """保存激活码"""
        key = self.key_var.get().strip()
        if not key:
            messagebox.showwarning("警告", "请输入激活码")
            return
        
        self.client.save_license(key)
        messagebox.showinfo("成功", "激活码已保存")
    
    def run_selected_program(self):
        """运行选中的程序"""
        # 获取选中的程序类型
        program_type = self.program_var.get()
        
        # 确保激活码已保存
        key = self.key_var.get().strip()
        if not key:
            messagebox.showwarning("警告", "请先输入并保存激活码")
            return
        
        # 检查许可证
        valid, message, data = self.client.check_license(key)
        if not valid:
            messagebox.showerror("错误", message)
            return
        
        # 验证程序权限 - 从正确的嵌套结构中获取allowed_programs
        license_info = data.get("license_info", {})
        allowed_programs = license_info.get("allowed_programs", [])
        if program_type not in allowed_programs:
            messagebox.showerror("错误", f"当前许可证不包含 {program_type} 程序的权限")
            return
        
        # 运行程序
        self.client.run_program(program_type)
    
    def on_closing(self):
        """窗口关闭时的处理"""
        # 确认是否关闭
        if messagebox.askokcancel("退出", "确定要退出程序吗？"):
            # 先终止浏览器进程
            self.client.kill_browser_processes()
            # 清理资源
            self.client.cleanup_temp_dir()
            # 关闭窗口
            self.root.destroy()
    
    def force_check_dependencies(self):
        """强制检查并更新依赖"""
        try:
            self.status_label.config(text="正在检查依赖，请稍候...")
            result = self.client.check_and_install_dependencies(force_check=True)
            if result:
                self.status_label.config(text="依赖检查完成，所有依赖已安装")
            else:
                self.status_label.config(text="依赖安装失败，请检查网络连接")
        except Exception as e:
            self.status_label.config(text=f"依赖检查失败: {str(e)}")

def hide_console():
    """在Windows平台上隐藏控制台窗口"""
    if platform.system() == "Windows":
        try:
            import ctypes
            hwnd = ctypes.windll.kernel32.GetConsoleWindow()
            if hwnd != 0:
                ctypes.windll.user32.ShowWindow(hwnd, 0)
                # 额外设置：将控制台窗口从任务栏中完全移除
                ctypes.windll.user32.SetWindowLongW(hwnd, -16, 0x80000000)
                ctypes.windll.user32.SetWindowPos(hwnd, 0, 0, 0, 0, 0, 0x0001)
            return True
        except Exception as e:
            logger.error(f"隐藏控制台失败: {str(e)}")
            return False
    return True

def main():
    # 检查是否已有实例在运行
    if not is_first_instance:
        # 已有实例在运行，直接退出
        logger.info("检测到已有实例在运行，退出程序")
        sys.exit(0)
    
    # 隐藏控制台窗口
    hide_console()
    
    # 创建客户端实例
    client = SimpleClient()
    
    # 在后台直接检查并安装依赖，不显示初始化窗口
    try:
        logger.info("在后台检查依赖...")
        client.check_and_install_dependencies()
        logger.info("依赖检查完成")
    except Exception as e:
        logger.error(f"后台依赖检查出错: {str(e)}")
        # 即使出错也继续执行
    
    # 设置AmazonUITheme的客户端实例（兼容性处理）
    try:
        if hasattr(AmazonUITheme, 'set_client'):
            AmazonUITheme.set_client(client)
        else:
            # 如果没有set_client方法，直接设置_client属性
            if hasattr(AmazonUITheme, '_client'):
                setattr(AmazonUITheme, '_client', client)
    except Exception as e:
        logger.debug(f"设置AmazonUITheme客户端实例失败: {str(e)}")
    
    # 确保图标已下载到应用数据目录
    client.check_and_download_icon()
    
    # 创建主窗口
    root = tk.Tk()
    
    # 设置应用程序ID (Windows平台)
    if platform.system() == "Windows":
        try:
            import ctypes
            app_id = "AmazonLicenseClient.App.1.0"  # 唯一应用ID
            ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID(app_id)
            
            # 提前尝试设置窗口图标
            icon_path = None
            if client.icon_data:
                # 创建临时图标文件
                import tempfile
                icon_fd, icon_path = tempfile.mkstemp(suffix='.ico')
                os.write(icon_fd, client.icon_data)
                os.close(icon_fd)
                
                # 设置图标
                try:
                    root.iconbitmap(icon_path)
                    logger.info(f"已设置窗口图标: {icon_path}")
                except Exception as e:
                    logger.debug(f"设置窗口图标失败: {str(e)}")
        except Exception as e:
            logger.debug(f"设置Windows应用ID失败: {str(e)}")
    
    # 使用client对象创建应用程序实例
    app = SimpleClientApp(root, client)
    
    # 运行应用
    root.mainloop()

if __name__ == "__main__":
    main() 