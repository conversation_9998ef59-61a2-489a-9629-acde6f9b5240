#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新系统配置文件
"""

# 服务器配置
SERVER_CONFIG = {
    # 授权服务器URL（用于新的API方式）
    "license_server_url": "http://**************:5000/",

    # 静态更新服务器URL（用于向后兼容）
    "update_server_url": "http://**************:5000/api/",

    # 当前版本
    "current_version": "2.1.0",

    # 更新检查间隔（秒）
    "check_interval": 3600,  # 1小时

    # 是否启用自动更新检查
    "auto_check_enabled": True,

    # 是否启用强制更新
    "force_update_enabled": False,

    # 下载超时时间（秒）
    "download_timeout": 300,  # 5分钟

    # 重试次数
    "max_retries": 3,
}

# 开发环境配置
DEV_CONFIG = {
    "license_server_url": "http://localhost:5000/",
    "update_server_url": "http://localhost:8000/updates/",
    "current_version": "2.1.0-dev",
    "check_interval": 60,  # 1分钟
    "auto_check_enabled": True,
    "force_update_enabled": False,
    "download_timeout": 60,
    "max_retries": 1,
}

# 测试环境配置
TEST_CONFIG = {
    "license_server_url": "https://test-server.com/",
    "update_server_url": "https://test-server.com/updates/",
    "current_version": "2.1.0-test",
    "check_interval": 300,  # 5分钟
    "auto_check_enabled": True,
    "force_update_enabled": False,
    "download_timeout": 120,
    "max_retries": 2,
}

def get_config(environment="production"):
    """
    获取指定环境的配置
    
    Args:
        environment: 环境名称 ("production", "development", "test")
    
    Returns:
        dict: 配置字典
    """
    if environment == "development":
        return DEV_CONFIG
    elif environment == "test":
        return TEST_CONFIG
    else:
        return SERVER_CONFIG

def update_config(environment="production", **kwargs):
    """
    更新配置
    
    Args:
        environment: 环境名称
        **kwargs: 要更新的配置项
    """
    config = get_config(environment)
    config.update(kwargs)
    return config

# 导出常用配置
CURRENT_CONFIG = get_config()
LICENSE_SERVER_URL = CURRENT_CONFIG["license_server_url"]
UPDATE_SERVER_URL = CURRENT_CONFIG["update_server_url"]
CURRENT_VERSION = CURRENT_CONFIG["current_version"]
